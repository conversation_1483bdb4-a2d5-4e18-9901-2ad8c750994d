import { loadEnvironment } from "../config/env-loader";
import type { OrderGift } from "../mikerudenko/marketplace-shared";
import { getGiftByIdForBot } from "../firebase/services/gift-functions";
import { LogOperations } from "../constants/bot-commands";

loadEnvironment();

// Create a simple logger service for gift service
const GiftServiceLogger = {
  logGetGiftByIdError(params: { error: unknown; giftId: string }) {
    const { log } = require("../utils/logger");
    log.error("Error getting gift by ID", params.error, {
      operation: LogOperations.GET_GIFT_BY_ID,
      giftId: params.giftId,
    });
  },
};

export async function getGiftById(giftId: string) {
  try {
    return await getGiftByIdForBot({
      giftId,
    });
  } catch (error) {
    GiftServiceLogger.logGetGiftByIdError({ error, giftId });
    return null;
  }
}

export async function getOrderGift(order: {
  gift?: OrderGift | null;
  giftId?: string | null;
}): Promise<OrderGift | null> {
  if (order.giftId) {
    const gift = await getGiftById(order.giftId);
    return gift ?? null;
  }

  return null;
}

interface OrderData {
  id: string;
  collectionId: string;
  buyerId?: string;
  sellerId?: string;
  buyer_tg_id?: string;
  seller_tg_id?: string;
  status: string;
  giftId?: string | null;
  [key: string]: any;
}

export async function getOrderOwnedGiftId(order: OrderData) {
  if (order.giftId) {
    const gift = await getOrderGift(order);
    return gift?.owned_gift_id || null;
  }

  return null;
}
