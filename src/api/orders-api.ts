import type { DocumentSnapshot } from 'firebase/firestore';
import {
  and,
  collection,
  getDocs,
  limit,
  or,
  orderBy,
  query,
  startAfter,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import { AppCloudFunctions } from '@/core.constants';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';
import {
  firebaseTimestampToDate,
  ORDERS_COLLECTION_NAME,
  OrderStatus,
} from '@/mikerudenko/marketplace-shared';
import { firebaseFunctions, firestore } from '@/root-context';

import { getCurrentUserId } from './auth-api';

export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: DocumentSnapshot | null;
  currentUserId?: string;
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
}

export interface MakePurchaseResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export interface MakeSecondaryMarketPurchaseResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
  newBuyerId: string;
  oldBuyerId: string;
  netAmountToOldBuyer: number;
  feeAmount: number;
  lockedAmount: number;
}

export type OrderType = 'buyers' | 'sellers';

const isValidSecondaryOrder = (order: OrderEntity): boolean => {
  return (
    order.status === 'paid' &&
    order.secondaryMarketPrice !== null &&
    order.secondaryMarketPrice !== undefined &&
    order.secondaryMarketPrice > 0
  );
};

const applyPriceFilter = (
  order: OrderEntity,
  filters: OrderFilters,
): boolean => {
  // Always prioritize secondary market price if available, otherwise use regular price
  const price = order.secondaryMarketPrice ?? order.price;

  if (filters.minPrice && price < filters.minPrice) return false;
  if (filters.maxPrice && price > filters.maxPrice) return false;

  return true;
};

/**
 * Sorts orders with client-side logic to handle secondary market prices properly.
 * For price sorting: Always prioritizes secondary market price over regular price.
 * Server-side sorting by 'price' field provides initial ordering, but client-side
 * sorting ensures secondary market prices take precedence in the final order.
 */
const sortUnifiedOrders = (
  orders: OrderEntity[],
  sortBy?: string,
): OrderEntity[] => {
  if (!sortBy) {
    return orders.sort((a, b) => {
      const aDate = a.createdAt
        ? firebaseTimestampToDate(a.createdAt).getTime()
        : 0;
      const bDate = b.createdAt
        ? firebaseTimestampToDate(b.createdAt).getTime()
        : 0;
      return bDate - aDate; // Default: newest first
    });
  }

  return orders.sort((a, b) => {
    if (sortBy.includes('price')) {
      // Always prioritize secondary market price if available
      const aPrice = a.secondaryMarketPrice ?? a.price;
      const bPrice = b.secondaryMarketPrice ?? b.price;

      return sortBy.includes('asc') ? aPrice - bPrice : bPrice - aPrice;
    } else {
      // Sort by date
      const aDate = a.createdAt
        ? firebaseTimestampToDate(a.createdAt).getTime()
        : 0;
      const bDate = b.createdAt
        ? firebaseTimestampToDate(b.createdAt).getTime()
        : 0;
      return sortBy.includes('asc') ? aDate - bDate : bDate - aDate;
    }
  });
};

export const getUnifiedBuyersOrders = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const pageSize = filters.limit ?? 20;

    // For price sorting, we need to handle primary and secondary market orders separately
    // then merge and sort them properly
    if (filters.sortBy?.includes('price')) {
      return await getUnifiedBuyersOrdersWithPriceSorting(filters, pageSize);
    }

    let q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      or(
        // Primary market orders: active status, has seller, no secondary price
        where('status', '==', 'active'),
        // Secondary market orders: paid status, has secondary price > 0
        and(
          where('status', '==', 'paid'),
          where('secondaryMarketPrice', '>', 0),
        ),
      ),
    );

    // Add collection filter if specified
    if (filters.collectionId) {
      q = query(q, where('collectionId', '==', filters.collectionId));
    }

    // Add server-side sorting
    const sortDirection = filters.sortBy?.includes('asc') ? 'asc' : 'desc';
    if (filters.sortBy?.includes('price')) {
      // For price sorting, use the 'price' field for server-side ordering
      q = query(q, orderBy('price', sortDirection));
    } else {
      // For date sorting, use createdAt
      q = query(q, orderBy('createdAt', sortDirection));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }

    // Fetch more documents to account for filtering
    q = query(q, limit(Math.max(pageSize * 2, 50)));

    const snapshot = await getDocs(q);
    const docs = snapshot.docs;

    // Process all documents and filter them
    const allProcessedOrders: OrderEntity[] = docs
      .map((doc) => ({ id: doc.id, ...doc.data() }) as OrderEntity)
      .filter((order) => {
        const isRegularOrder =
          order.status === 'active' &&
          order.sellerId &&
          !order.buyerId &&
          !order.secondaryMarketPrice;
        const isSecondaryOrder = isValidSecondaryOrder(order);

        if (isRegularOrder || isSecondaryOrder) {
          return applyPriceFilter(order, filters);
        }

        return false;
      });

    // Apply pagination to filtered results
    const orders = allProcessedOrders.slice(0, pageSize);
    // hasMore is true if we have more filtered orders than pageSize, OR if we fetched the maximum and might have more
    const hasMore =
      allProcessedOrders.length > pageSize ||
      (docs.length >= Math.max(pageSize * 2, 50) &&
        allProcessedOrders.length >= pageSize);

    // Find the last document from the original docs that corresponds to our last order
    const lastDoc =
      hasMore && orders.length > 0
        ? docs.find((doc) => doc.id === orders[orders.length - 1].id) || null
        : null;

    return { orders, lastDoc, hasMore };
  } catch (error) {
    console.error('Error fetching unified buyers orders:', error);
    throw error;
  }
};

// Helper function to handle price sorting for buyers orders
const getUnifiedBuyersOrdersWithPriceSorting = async (
  filters: OrderFilters,
  pageSize: number,
): Promise<PaginatedOrdersResult> => {
  const sortDirection = filters.sortBy?.includes('asc') ? 'asc' : 'desc';

  // Query primary market orders (active status, sort by price)
  let primaryQuery = query(
    collection(firestore, ORDERS_COLLECTION_NAME),
    where('status', '==', 'active'),
    orderBy('price', sortDirection),
  );

  // Query secondary market orders (paid status with secondaryMarketPrice, sort by secondaryMarketPrice)
  let secondaryQuery = query(
    collection(firestore, ORDERS_COLLECTION_NAME),
    where('status', '==', 'paid'),
    where('secondaryMarketPrice', '>', 0),
    orderBy('secondaryMarketPrice', sortDirection),
  );

  // Add collection filter if specified
  if (filters.collectionId) {
    primaryQuery = query(
      primaryQuery,
      where('collectionId', '==', filters.collectionId),
    );
    secondaryQuery = query(
      secondaryQuery,
      where('collectionId', '==', filters.collectionId),
    );
  }

  // For pagination with price sorting, we need to fetch more data and merge
  const fetchSize = Math.max(pageSize * 5, 200); // Increased fetch size for better pagination

  primaryQuery = query(primaryQuery, limit(fetchSize));
  secondaryQuery = query(secondaryQuery, limit(fetchSize));

  const [primarySnapshot, secondarySnapshot] = await Promise.all([
    getDocs(primaryQuery),
    getDocs(secondaryQuery),
  ]);

  // Process primary market orders
  const primaryOrders: OrderEntity[] = primarySnapshot.docs
    .map((doc) => ({ id: doc.id, ...doc.data() }) as OrderEntity)
    .filter((order) => {
      const isValidOrder =
        order.sellerId && !order.buyerId && !order.secondaryMarketPrice;
      return isValidOrder && applyPriceFilter(order, filters);
    });

  // Process secondary market orders
  const secondaryOrders: OrderEntity[] = secondarySnapshot.docs
    .map((doc) => ({ id: doc.id, ...doc.data() }) as OrderEntity)
    .filter((order) => {
      const isValidOrder = isValidSecondaryOrder(order);
      return isValidOrder && applyPriceFilter(order, filters);
    });

  // Merge and sort all orders by their effective price
  const allOrders = [...primaryOrders, ...secondaryOrders];
  const sortedOrders = sortUnifiedOrders(allOrders, filters.sortBy);

  // Apply pagination to the sorted results
  let startIndex = 0;
  if (filters.lastDoc) {
    const lastDocIndex = sortedOrders.findIndex(
      (order) => order.id === filters.lastDoc?.id,
    );
    startIndex = lastDocIndex >= 0 ? lastDocIndex + 1 : 0;
  }

  const endIndex = startIndex + pageSize;
  const paginatedOrders = sortedOrders.slice(startIndex, endIndex);
  const hasMore = endIndex < sortedOrders.length;

  return {
    orders: paginatedOrders,
    lastDoc:
      paginatedOrders.length > 0
        ? ({
            id: paginatedOrders[paginatedOrders.length - 1].id,
          } as unknown as DocumentSnapshot)
        : null,
    hasMore,
  };
};

export const getUnifiedSellerOrders = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const pageSize = filters.limit ?? 20;

    // For price sorting, we need to handle primary and secondary market orders separately
    // then merge and sort them properly
    if (filters.sortBy?.includes('price')) {
      return await getUnifiedSellerOrdersWithPriceSorting(filters, pageSize);
    }

    let q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      or(
        // Primary market orders: active status, has buyer, no secondary price
        where('status', '==', 'active'),
        // Secondary market orders: paid status, has secondary price > 0
        and(
          where('status', '==', 'paid'),
          where('secondaryMarketPrice', '>', 0),
        ),
      ),
    );

    // Add collection filter if specified
    if (filters.collectionId) {
      q = query(q, where('collectionId', '==', filters.collectionId));
    }

    // For non-price sorting, use server-side sorting
    const sortDirection = filters.sortBy?.includes('asc') ? 'asc' : 'desc';
    if (filters.sortBy?.includes('price')) {
      // For price sorting, use the 'price' field for server-side ordering
      q = query(q, orderBy('price', sortDirection));
    } else {
      // For date sorting, use createdAt
      q = query(q, orderBy('createdAt', sortDirection));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }

    // Fetch more documents to account for filtering
    q = query(q, limit(Math.max(pageSize * 2, 50)));

    const snapshot = await getDocs(q);
    const docs = snapshot.docs;

    // Process all documents and filter them
    const allProcessedOrders: OrderEntity[] = docs
      .map((doc) => ({ id: doc.id, ...doc.data() }) as OrderEntity)
      .filter((order) => {
        const isRegularOrder =
          order.status === 'active' &&
          order.buyerId &&
          !order.sellerId &&
          !order.secondaryMarketPrice;
        const isSecondaryOrder = isValidSecondaryOrder(order);

        if (isRegularOrder || isSecondaryOrder) {
          return applyPriceFilter(order, filters);
        }

        return false;
      });

    // Apply pagination to filtered results
    const orders = allProcessedOrders.slice(0, pageSize);
    // hasMore is true if we have more filtered orders than pageSize, OR if we fetched the maximum and might have more
    const hasMore =
      allProcessedOrders.length > pageSize ||
      (docs.length >= Math.max(pageSize * 2, 50) &&
        allProcessedOrders.length >= pageSize);

    // Find the last document from the original docs that corresponds to our last order
    const lastDoc =
      hasMore && orders.length > 0
        ? docs.find((doc) => doc.id === orders[orders.length - 1].id) || null
        : null;

    return { orders, lastDoc, hasMore };
  } catch (error) {
    console.error('Error fetching unified seller orders:', error);
    throw error;
  }
};

// Helper function to handle price sorting for seller orders
const getUnifiedSellerOrdersWithPriceSorting = async (
  filters: OrderFilters,
  pageSize: number,
): Promise<PaginatedOrdersResult> => {
  const sortDirection = filters.sortBy?.includes('asc') ? 'asc' : 'desc';

  // Query primary market orders (active status, sort by price)
  let primaryQuery = query(
    collection(firestore, ORDERS_COLLECTION_NAME),
    where('status', '==', 'active'),
    orderBy('price', sortDirection),
  );

  // Query secondary market orders (paid status with secondaryMarketPrice, sort by secondaryMarketPrice)
  let secondaryQuery = query(
    collection(firestore, ORDERS_COLLECTION_NAME),
    where('status', '==', 'paid'),
    where('secondaryMarketPrice', '>', 0),
    orderBy('secondaryMarketPrice', sortDirection),
  );

  // Add collection filter if specified
  if (filters.collectionId) {
    primaryQuery = query(
      primaryQuery,
      where('collectionId', '==', filters.collectionId),
    );
    secondaryQuery = query(
      secondaryQuery,
      where('collectionId', '==', filters.collectionId),
    );
  }

  // For pagination with price sorting, we need to fetch more data and merge
  const fetchSize = Math.max(pageSize * 5, 200); // Increased fetch size for better pagination

  primaryQuery = query(primaryQuery, limit(fetchSize));
  secondaryQuery = query(secondaryQuery, limit(fetchSize));

  const [primarySnapshot, secondarySnapshot] = await Promise.all([
    getDocs(primaryQuery),
    getDocs(secondaryQuery),
  ]);

  // Process primary market orders (for sellers: orders with buyerId, no sellerId)
  const primaryOrders: OrderEntity[] = primarySnapshot.docs
    .map((doc) => ({ id: doc.id, ...doc.data() }) as OrderEntity)
    .filter((order) => {
      const isValidOrder =
        order.buyerId && !order.sellerId && !order.secondaryMarketPrice;
      return isValidOrder && applyPriceFilter(order, filters);
    });

  // Process secondary market orders
  const secondaryOrders: OrderEntity[] = secondarySnapshot.docs
    .map((doc) => ({ id: doc.id, ...doc.data() }) as OrderEntity)
    .filter((order) => {
      const isValidOrder = isValidSecondaryOrder(order);
      return isValidOrder && applyPriceFilter(order, filters);
    });

  // Merge and sort all orders by their effective price
  const allOrders = [...primaryOrders, ...secondaryOrders];
  const sortedOrders = sortUnifiedOrders(allOrders, filters.sortBy);

  // Apply pagination to the sorted results
  let startIndex = 0;
  if (filters.lastDoc) {
    const lastDocIndex = sortedOrders.findIndex(
      (order) => order.id === filters.lastDoc?.id,
    );
    startIndex = lastDocIndex >= 0 ? lastDocIndex + 1 : 0;
  }

  const endIndex = startIndex + pageSize;
  const paginatedOrders = sortedOrders.slice(startIndex, endIndex);
  const hasMore = endIndex < sortedOrders.length;

  return {
    orders: paginatedOrders,
    lastDoc:
      paginatedOrders.length > 0
        ? ({
            id: paginatedOrders[paginatedOrders.length - 1].id,
          } as unknown as DocumentSnapshot)
        : null,
    hasMore,
  };
};

// Replace the old functions with the new unified implementations
export const getOrdersForBuyers = getUnifiedBuyersOrders;
export const getOrdersForSellers = getUnifiedSellerOrders;

export const makePurchaseAsBuyer = async (
  orderId: string,
): Promise<MakePurchaseResponse> => {
  try {
    const makePurchaseAsBuyerFunction = httpsCallable<
      { buyerId: string; orderId: string },
      MakePurchaseResponse
    >(firebaseFunctions, AppCloudFunctions.makePurchaseAsBuyer);

    const currentUserId = getCurrentUserId();

    if (!currentUserId) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsBuyerFunction({
      buyerId: currentUserId,
      orderId,
    });

    return result.data;
  } catch (error) {
    console.error('Error making purchase as buyer:', error);
    throw error;
  }
};

export const makePurchaseAsSeller = async (
  orderId: string,
): Promise<MakePurchaseResponse> => {
  try {
    const makePurchaseAsSellerFunction = httpsCallable<
      { sellerId: string; orderId: string },
      MakePurchaseResponse
    >(firebaseFunctions, AppCloudFunctions.makePurchaseAsSeller);

    const currentUserId = getCurrentUserId();

    if (!currentUserId) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsSellerFunction({
      sellerId: currentUserId,
      orderId,
    });

    return result.data;
  } catch (error) {
    console.error('Error making purchase as seller:', error);
    throw error;
  }
};

export const makeSecondaryMarketPurchase = async (orderId: string) => {
  try {
    const makeSecondaryMarketPurchaseFunction = httpsCallable<
      { orderId: string },
      MakeSecondaryMarketPurchaseResponse
    >(firebaseFunctions, AppCloudFunctions.makeSecondaryMarketPurchase);

    const result = await makeSecondaryMarketPurchaseFunction({ orderId });

    return result.data;
  } catch (error) {
    console.error('Error making secondary market purchase:', error);
    throw error;
  }
};

export const getActivityOrders = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const pageSize = filters.limit ?? 20;

    // Build query for activity orders (paid, gift_sent_to_relayer, fulfilled)
    let q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      or(
        where('status', '==', OrderStatus.PAID),
        where('status', '==', OrderStatus.GIFT_SENT_TO_RELAYER),
        where('status', '==', OrderStatus.FULFILLED),
      ),
    );

    // Add collection filter if specified
    if (filters.collectionId) {
      q = query(q, where('collectionId', '==', filters.collectionId));
    }

    // Apply sorting - use 'amount' field from database
    if (filters.sortBy === 'price_desc') {
      q = query(q, orderBy('amount', 'desc'));
    } else if (filters.sortBy === 'price_asc') {
      q = query(q, orderBy('amount', 'asc'));
    } else {
      // Default to date descending (newest first)
      q = query(q, orderBy('updatedAt', 'desc'));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }

    q = query(q, limit(pageSize + 1));

    const snapshot = await getDocs(q);
    const docs = snapshot.docs;
    const hasMore = docs.length > pageSize;
    const ordersToReturn = hasMore ? docs.slice(0, pageSize) : docs;

    // Transform documents and apply price filtering
    let orders: OrderEntity[] = ordersToReturn
      .map((doc) => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          // Map 'amount' field from database to 'price' field expected by frontend
          price: data.amount ?? data.price ?? 0,
        } as OrderEntity;
      })
      .filter((order) => applyPriceFilter(order, filters));

    // Apply client-side sorting if price sorting is requested
    if (filters.sortBy?.includes('price')) {
      orders = sortUnifiedOrders(orders, filters.sortBy);
    }

    const lastDoc = hasMore ? ordersToReturn[ordersToReturn.length - 1] : null;

    return { orders, lastDoc, hasMore };
  } catch (error) {
    console.error('Error fetching activity orders:', error);
    throw error;
  }
};
