import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const buyerOrderLogger = createServiceLogger("buyer-order-function");

export const BuyerOrderLogger = {
  logCreateOrderError({
    error,
    operation,
    requestData,
    userId,
  }: {
    error: unknown;
    operation: LogOperations;
    requestData?: any;
    userId?: string;
  }) {
    buyerOrderLogger.logError("Error creating buyer order", error, operation, {
      requestData,
      userId,
    });
  },

  logPurchaseError({
    error,
    operation,
    buyerId,
    orderId,
  }: {
    error: unknown;
    operation: LogOperations;
    buyerId?: string;
    orderId?: string;
  }) {
    buyerOrderLogger.logError("Error in buyer purchase", error, operation, {
      buyerId,
      orderId,
    });
  },
};
