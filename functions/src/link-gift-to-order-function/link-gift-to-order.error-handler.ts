import { HttpsError } from "firebase-functions/v2/https";
import { LinkGiftToOrderLogger } from "./link-gift-to-order.logger";

export function throwGiftNotFound(): never {
  throw new HttpsError("not-found", "Gift not found");
}

export function throwOrderNotFound(): never {
  throw new HttpsError("not-found", "Order not found");
}

export function throwGiftNotOwnedByUser(): never {
  throw new HttpsError(
    "permission-denied",
    "Gift does not belong to the current user"
  );
}

export function throwOrderNotOwnedByUser(): never {
  throw new HttpsError(
    "permission-denied",
    "User is not the seller of this order"
  );
}

export function throwInvalidOrderStatus(status: string): never {
  throw new HttpsError(
    "failed-precondition",
    `Order status must be 'created' or 'paid', but was '${status}'`
  );
}

export function throwGiftCollectionMismatch(): never {
  throw new HttpsError(
    "failed-precondition",
    "Gift and order must be from the same collection"
  );
}

export function handleLinkGiftToOrderError(error: unknown) {
  LinkGiftToOrderLogger.logLinkGiftToOrderError(error);

  if (error instanceof HttpsError) {
    return {
      success: false,
      message: error.message,
    };
  }

  return {
    success: false,
    message: "An unexpected error occurred while linking gift to order",
  };
}
