import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const orderCancellationServiceLogger = createServiceLogger(
  "order-cancellation-service"
);

export const OrderCancellationServiceLogger = {
  logOrderCancellationStarted({
    orderId,
    userId,
    operation,
  }: {
    orderId: string;
    userId: string;
    operation: string;
  }) {
    orderCancellationServiceLogger.logInfo(
      "Order cancellation started",
      LogOperations.CANCELLATION_STARTED,
      {
        orderId,
        userId,
        operation,
      }
    );
  },

  logOrderCancellationCompleted({
    orderId,
    userId,
    feeApplied,
    feeType,
  }: {
    orderId: string;
    userId: string;
    feeApplied: boolean;
    feeType?: string;
  }) {
    orderCancellationServiceLogger.logInfo(
      "Order cancellation completed",
      LogOperations.CANCELLATION_COMPLETED,
      {
        orderId,
        userId,
        feeApplied,
        feeType,
      }
    );
  },

  logOrderCancellationError({
    error,
    operation,
    orderId,
    userId,
  }: {
    error: unknown;
    operation: string;
    orderId?: string;
    userId?: string;
  }) {
    orderCancellationServiceLogger.logError(
      `Error in order cancellation: ${operation}`,
      error,
      LogOperations.CANCEL_ORDER,
      {
        orderId,
        userId,
      }
    );
  },
};
