import { defineMessages } from 'react-intl';

export const resellOrderDrawerMessages = defineMessages({
  resellMyOrder: {
    id: 'marketplace.resell.resellMyOrder',
    defaultMessage: 'Resell My Order',
  },
  selectOrderToResell: {
    id: 'marketplace.resell.selectOrderToResell',
    defaultMessage:
      'Select an order you purchased to resell on the secondary market',
  },
  loadingYourOrders: {
    id: 'marketplace.resell.loadingYourOrders',
    defaultMessage: 'Loading your orders...',
  },
  noOrdersFoundToResell: {
    id: 'marketplace.resell.noOrdersFoundToResell',
    defaultMessage: 'No orders found that can be resold',
  },
  updateResaleOrder: {
    id: 'marketplace.resell.updateResaleOrder',
    defaultMessage: 'Update Resale Order',
  },
  createResaleOrder: {
    id: 'marketplace.resell.createResaleOrder',
    defaultMessage: 'Create Resale Order',
  },
  cancel: {
    id: 'marketplace.resell.cancel',
    defaultMessage: 'Cancel',
  },
});
