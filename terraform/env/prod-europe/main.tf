terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.28.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

# Enable required APIs
resource "google_project_service" "required_apis" {
  for_each = toset([
    "run.googleapis.com",
    "artifactregistry.googleapis.com",
    "cloudbuild.googleapis.com",
    "iam.googleapis.com",
    "redis.googleapis.com",
    "compute.googleapis.com"
  ])

  service            = each.value
  disable_on_destroy = false
  project            = var.project_id
}

# IAM Module
module "iam" {
  source = "../../modules/iam"
  
  project_id = var.project_id
}

# Artifact Registry Module
module "artifact_registry" {
  source = "../../modules/artifact-registry"
  
  project_id    = var.project_id
  region        = var.region
  repository_id = "${var.service_name}-europe"
  description   = "Docker repository for ${var.service_name} - Production"
  
  api_dependencies = [google_project_service.required_apis]
}

# Redis Module
# module "redis" {
#   source = "../../modules/redis"
  
#   project_id        = var.project_id
#   region            = var.region
#   instance_name     = "${var.service_name}-redis"
#   tier              = "STANDARD_HA"
#   memory_size_gb    = 1
#   redis_version     = "REDIS_5_0"
#   auth_enabled      = false
  
#   api_dependencies = [google_project_service.required_apis]
# }

# Cloud Run Module
module "cloud_run" {
  source = "../../modules/cloud-run"
  
  project_id    = var.project_id
  region        = var.region
  service_name  = var.service_name
  image_url     = "${var.region}-docker.pkg.dev/${var.project_id}/${var.service_name}-europe/${var.service_name}:latest"
  
  service_account_email = module.iam.cloud_run_service_sa_email
  
  environment_variables = {
    NODE_ENV               = "production"
    # REDIS_HOST             = module.redis.redis_host
    # REDIS_PORT             = tostring(module.redis.redis_port)
    FIREBASE_FUNCTIONS_URL = "https://${var.region}-${var.project_id}.cloudfunctions.net"
  }
  
  annotations = {
    "autoscaling.knative.dev/maxScale"    = "10"
    "run.googleapis.com/cpu-throttling"   = "false"
    "run.googleapis.com/vpc-access-egress" = "private-ranges-only"
    "run.googleapis.com/network-interfaces" = jsonencode([{
      network    = "projects/${var.project_id}/global/networks/default"
      subnetwork = "projects/${var.project_id}/regions/${var.region}/subnetworks/default"
    }])
  }
  
  # Use depends_on at the module level instead of passing dependencies
  depends_on = [
    google_project_service.required_apis,
    # module.redis
  ]
}
