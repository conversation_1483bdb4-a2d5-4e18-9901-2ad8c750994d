import { log } from "../../utils/logger";

export class WithdrawGiftCallbackLogger {
  static logWithdrawGiftCallbackStarted(params: {
    tgId: string;
    giftId: string;
    chatId?: number;
  }) {
    const { tgId, giftId, chatId } = params;
    log.info("Withdraw gift callback started", {
      operation: "withdraw_gift_callback_started",
      tgId,
      giftId,
      chatId,
    });
  }

  static logGiftIdSavedToSession(params: { tgId: string; giftId: string }) {
    const { tgId, giftId } = params;
    log.info("Gift ID saved to user session", {
      operation: "gift_id_saved_to_session",
      tgId,
      giftId,
    });
  }

  static logWithdrawGiftCallbackCompleted(params: {
    tgId: string;
    giftId: string;
  }) {
    const { tgId, giftId } = params;
    log.info("Withdraw gift callback completed successfully", {
      operation: "withdraw_gift_callback_completed",
      tgId,
      giftId,
    });
  }

  static logWithdrawGiftCallbackError(params: {
    error: unknown;
    tgId?: string;
    chatId?: number;
  }) {
    const { error, tgId, chatId } = params;
    log.error("Error in withdraw gift callback", error, {
      operation: "withdraw_gift_callback_error",
      tgId,
      chatId,
    });
  }
}
