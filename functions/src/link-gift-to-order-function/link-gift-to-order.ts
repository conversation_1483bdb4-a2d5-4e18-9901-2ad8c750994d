import { onCall, HttpsError } from "firebase-functions/v2/https";
import { linkGiftToOrderService } from "./link-gift-to-order.service";
import { LinkGiftToOrderLogger } from "./link-gift-to-order.logger";
import { handleLinkGiftToOrderError } from "./link-gift-to-order.error-handler";
import { commonFunctionsConfig } from "../constants";

interface LinkGiftToOrderRequest {
  giftId: string;
  orderId: string;
}

export const linkGiftToOrder = onCall(
  commonFunctionsConfig,
  async (request) => {
    try {
      LinkGiftToOrderLogger.logLinkGiftToOrderFunctionCalled({
        giftId: request.data?.giftId,
        orderId: request.data?.orderId,
        userId: request.auth?.uid,
      });

      // Validate request data
      if (!request.data) {
        throw new HttpsError("invalid-argument", "Request data is required");
      }

      if (!request.auth?.uid) {
        throw new HttpsError("unauthenticated", "User must be authenticated");
      }

      const { giftId, orderId } = request.data as LinkGiftToOrderRequest;

      if (!giftId || !orderId) {
        throw new HttpsError(
          "invalid-argument",
          "giftId and orderId are required"
        );
      }

      const result = await linkGiftToOrderService({
        giftId,
        orderId,
        userId: request.auth.uid,
      });

      LinkGiftToOrderLogger.logLinkGiftToOrderCompleted({
        giftId,
        orderId,
        userId: request.auth.uid,
        success: result.success,
      });

      return result;
    } catch (error) {
      return handleLinkGiftToOrderError(error);
    }
  }
);
