'use client';

import { useIntl } from 'react-intl';

import { TgsOrImageGift } from '@/components/tgs/tgs-or-image-gift';
import { Button } from '@/components/ui/button';
import { CollectionSelect } from '@/components/ui/collection-select';
import { Label } from '@/components/ui/label';
import {
  type CollectionEntity,
  CollectionStatus,
  type GiftEntity,
  UserType,
} from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';

import { createOrderDrawerMessages } from './intl/create-order-drawer.messages';

interface CreateOrderCollectionSectionProps {
  collections: CollectionEntity[];
  selectedCollection: string;
  onCollectionChange: (collectionId: string) => void;
  selectedCollectionData: CollectionEntity | undefined;
  userType: UserType;
  availableGifts: GiftEntity[];
  selectedGift: GiftEntity | null;
  loadingGifts: boolean;
  onShowGiftSelection: () => void;
  isAnimatedCollection: boolean;
}

export function CreateOrderCollectionSection({
  selectedCollection,
  onCollectionChange,
  selectedCollectionData,
  userType,
  availableGifts,
  selectedGift,
  loadingGifts,
  onShowGiftSelection,
  isAnimatedCollection,
}: CreateOrderCollectionSectionProps) {
  const { formatMessage: t } = useIntl();
  const { collections } = useRootContext();

  const areLockAndCollateralHidden =
    selectedCollectionData?.status === CollectionStatus.MARKET;
  const isSeller = userType === UserType.SELLER;
  const showGiftSelection = areLockAndCollateralHidden && isSeller;

  // Show market collection notice only for sellers and when no gift is selected
  const showMarketCollectionNotice =
    areLockAndCollateralHidden && isSeller && !selectedGift;

  return (
    <div>
      <CollectionSelect
        showAllOption={false}
        animated={isAnimatedCollection}
        collections={collections}
        value={selectedCollection}
        onValueChange={onCollectionChange}
        placeholder={t(createOrderDrawerMessages.selectCollection)}
        className="mt-2"
        userType={userType}
      />

      {showMarketCollectionNotice && (
        <div className="mt-3 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
          <div className="flex items-start gap-2">
            <div className="w-5 h-5 rounded-full bg-yellow-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-yellow-400 text-xs font-bold">!</span>
            </div>
            <div className="text-sm text-yellow-200">
              <p className="font-medium mb-1">
                {t(createOrderDrawerMessages.marketCollectionNotice)}
              </p>
              <p className="text-yellow-300/90">
                {t(createOrderDrawerMessages.marketCollectionDescription)}
              </p>
            </div>
          </div>
        </div>
      )}

      {showGiftSelection && (
        <div className="mt-3">
          <Label className="text-sm font-medium text-[#f5f5f5]">
            {t(createOrderDrawerMessages.selectGiftToAttach)}
          </Label>
          {loadingGifts ? (
            <div className="mt-2 p-3 bg-[#232e3c]/50 border border-[#3a4a5c]/50 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#6ab2f2]"></div>
                <span className="text-[#708499] text-sm">
                  {t(createOrderDrawerMessages.loadingAvailableGifts)}
                </span>
              </div>
            </div>
          ) : availableGifts.length === 0 ? (
            <div className="mt-2 p-3 bg-[#232e3c]/50 border border-[#3a4a5c]/50 rounded-lg">
              <p className="text-[#708499] text-sm">
                {t(createOrderDrawerMessages.noGiftsAvailable)}
              </p>
            </div>
          ) : (
            <Button
              variant="outline"
              onClick={onShowGiftSelection}
              className="mt-2 w-full bg-[#232e3c]/50 border-[#3a4a5c]/50 text-[#f5f5f5] hover:bg-[#232e3c] hover:border-[#6ab2f2] h-auto p-3"
            >
              {selectedGift ? (
                <div className="flex items-center gap-3 w-full">
                  <div className="w-10 h-10 rounded-lg overflow-hidden bg-[#17212b] flex-shrink-0">
                    <TgsOrImageGift
                      isImage={true}
                      gift={selectedGift}
                      className="w-full h-full"
                      style={{ width: '100%', height: '100%' }}
                    />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="text-[#f5f5f5] font-medium">
                      {collections.find(
                        (c) => c.id === selectedGift.collectionId,
                      )?.name || 'Unknown Collection'}
                    </div>
                    <div className="text-[#708499] text-sm">
                      {selectedGift.model?.name
                        ? `(${selectedGift.model.name})`
                        : ''}
                    </div>
                  </div>
                </div>
              ) : (
                t(createOrderDrawerMessages.selectGiftButton, {
                  count: availableGifts.length,
                })
              )}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
