import { log } from "./utils/logger";
import { LogOperations } from "./constants/bot-commands";

export const IndexLogger = {
  logStartupConfiguration(params: {
    NODE_ENV: string;
    PORT?: number | string | undefined;
    WEBHOOK_URL?: string | undefined;
    processId: number;
  }) {
    log.info("Startup Configuration", {
      operation: LogOperations.BOT_STARTUP,
      NODE_ENV: params.NODE_ENV,
      PORT: params.PORT,
      WEBHOOK_URL: params.WEBHOOK_URL
        ? params.WEBHOOK_URL.substring(0, 50) + "..."
        : "Not set",
      processId: params.processId,
    });
  },

  logBotLaunchFailed(params: { error: any }) {
    log.error("Failed to launch bot", params.error, {
      operation: LogOperations.BOT_STARTUP,
      mode: "polling",
    });
  },

  logBotConflictWarning() {
    log.warn("Another bot instance might be running. Please stop it first.", {
      operation: LogOperations.BOT_STARTUP,
      mode: "polling",
      error_type: "conflict",
    });
  },

  logMenuButtonSetFailed(params: { error: unknown; webAppUrl: string }) {
    log.error("Failed to set menu button", params.error, {
      operation: LogOperations.BOT_CONFIGURATION,
      component: "menu_button",
      webAppUrl: params.webAppUrl,
    });
  },

  logCommandsSetFailed(params: { error: unknown }) {
    log.warn("Failed to set commands", {
      operation: LogOperations.BOT_CONFIGURATION,
      component: "commands",
      error: params.error,
    });
  },

  logBotDescriptionSetFailed(params: { error: unknown }) {
    log.error("Failed to set bot description", params.error, {
      operation: LogOperations.BOT_CONFIGURATION,
      component: "description",
    });
  },

  logBotStartFailed(params: { error: unknown }) {
    log.error("Failed to start bot", params.error, {
      operation: LogOperations.BOT_STARTUP,
    });
  },

  logGracefulShutdownStarted(params: { signal: string }) {
    log.info(`Received ${params.signal}, shutting down gracefully`, {
      operation: LogOperations.BOT_SHUTDOWN,
      signal: params.signal,
    });
  },

  logGracefulShutdownTimeout(params: { signal: string }) {
    log.error("Graceful shutdown timeout, forcing exit", undefined, {
      operation: LogOperations.BOT_SHUTDOWN,
      signal: params.signal,
    });
  },

  logHttpServerStopping() {
    log.info("Stopping HTTP server", {
      operation: LogOperations.BOT_SHUTDOWN,
      step: "http_server",
    });
  },

  logWebhookCleanupSkipped() {
    log.info("Skipping webhook cleanup for faster shutdown", {
      operation: LogOperations.BOT_SHUTDOWN,
      step: "webhook_cleanup_skip",
    });
  },

  logGracefulShutdownCompleted() {
    log.info("Graceful shutdown completed", {
      operation: LogOperations.BOT_SHUTDOWN,
      status: "completed",
    });
  },

  logShutdownError(params: { error: unknown }) {
    log.error("Error during shutdown", params.error, {
      operation: LogOperations.BOT_SHUTDOWN,
      status: "error",
    });
  },

  logBotStarting() {
    log.botLog("Starting Marketplace Bot", {
      operation: LogOperations.BOT_STARTUP,
    });
  },

  logMenuButtonConfigured() {
    log.botLog("Menu button configured successfully", {
      operation: LogOperations.BOT_CONFIGURATION,
      component: "menu_button",
      status: "success",
      menuButtonText: "PREM",
      webAppUrl: process.env.WEB_APP_URL?.substring(0, 50) + "...",
    });
  },

  logMenuButtonVerification(params: {
    menuButtonType: string;
    menuButtonText?: string;
    webAppUrl?: string;
  }) {
    log.botLog("Menu button verification", {
      operation: LogOperations.BOT_CONFIGURATION,
      component: "menu_button_verification",
      menuButtonType: params.menuButtonType,
      menuButtonText: params.menuButtonText,
      webAppUrl: params.webAppUrl,
    });
  },

  logCommandsConfigured() {
    log.botLog("Commands configured", {
      operation: LogOperations.BOT_CONFIGURATION,
      component: "commands",
      status: "success",
    });
  },

  logBotDescriptionConfigured(params: {
    description: string;
    shortDescription: string;
  }) {
    log.botLog("Bot description configured", {
      operation: LogOperations.BOT_CONFIGURATION,
      component: "description",
      status: "success",
      description: params.description.substring(0, 100) + "...",
      shortDescription: params.shortDescription,
    });
  },

  logBotSetupCompleted() {
    log.botLog("Bot setup completed successfully", {
      operation: LogOperations.BOT_STARTUP,
      status: "completed",
      features: [
        "My Buy Orders",
        "Contact Support",
        "Open Marketplace (Web App)",
      ],
    });
  },

  logBotStopping() {
    log.botLog("Stopping bot", {
      operation: LogOperations.BOT_SHUTDOWN,
      step: "bot",
    });
  },
};
