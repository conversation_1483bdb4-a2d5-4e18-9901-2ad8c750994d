import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export interface LogContext {
  userId?: string;
  orderId?: string;
  transactionId?: string;
  collectionId?: string;
  operation?: string;
  service?: string;
  [key: string]: any;
}

class Logger {
  info(message: string, context?: LogContext) {
    logger.info(message, context);
  }

  error(message: string, error?: Error | unknown, context?: LogContext) {
    const logData = {
      ...context,
      error:
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
          : error,
    };
    logger.error(message, logData);
  }

  warn(message: string, context?: LogContext) {
    logger.warn(message, context);
  }

  debug(message: string, context?: LogContext) {
    logger.debug(message, context);
  }

  // Specialized logging methods for common operations
  transactionLog(
    message: string,
    transactionData: {
      transactionId: string;
      amount?: number;
      sender?: string;
      userId?: string;
      userTgId?: string;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: LogOperations.TRANSACTION_PROCESSING,
      ...transactionData,
    });
  }

  orderLog(
    message: string,
    orderData: {
      orderId: string;
      userId?: string;
      status?: string;
      price?: number;
      collectionId?: string;
    }
  ) {
    this.info(message, {
      operation: LogOperations.ORDER_PROCESSING,
      ...orderData,
    });
  }

  balanceLog(
    message: string,
    balanceData: {
      userId: string;
      amount?: number;
      operation?: string;
      currentBalance?: number;
      lockedAmount?: number;
      originalAmount?: number;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: LogOperations.BALANCE_OPERATION,
      ...balanceData,
    });
  }

  feeLog(
    message: string,
    feeData: {
      userId?: string;
      feeAmount: number;
      feeType: string;
      orderId?: string;
      netAmount?: number;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: LogOperations.FEE_PROCESSING,
      ...feeData,
    });
  }

  monitorLog(
    message: string,
    monitorData: {
      monitor: string;
      status?: string;
      count?: number;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: LogOperations.MONITORING,
      ...monitorData,
    });
  }

  botLog(
    message: string,
    botData: {
      userId?: string;
      chatId?: string;
      operation?: string;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: LogOperations.BOT_OPERATION,
      ...botData,
    });
  }
}

export const log = new Logger();

// Pure function to create log context
export function createLogContext(
  serviceName: string,
  operation: LogOperations,
  additionalContext?: LogContext
): LogContext {
  return {
    service: serviceName,
    operation,
    ...additionalContext,
  };
}

// Higher-order function that creates logger functions for a specific service
export function createServiceLogger(serviceName: string) {
  const createContext = (operation: LogOperations, context?: LogContext) =>
    createLogContext(serviceName, operation, context);

  return {
    logInfo: (
      message: string,
      operation: LogOperations,
      context?: LogContext
    ) => {
      log.info(message, createContext(operation, context));
    },

    logError: (
      message: string,
      error: unknown,
      operation: LogOperations,
      context?: LogContext
    ) => {
      log.error(message, error, createContext(operation, context));
    },

    logWarn: (
      message: string,
      operation: LogOperations,
      context?: LogContext
    ) => {
      log.warn(message, createContext(operation, context));
    },

    logDebug: (
      message: string,
      operation: LogOperations,
      context?: LogContext
    ) => {
      log.debug(message, createContext(operation, context));
    },
  };
}

// Individual logging functions that can be composed
export function logInfo(
  serviceName: string,
  message: string,
  operation: LogOperations,
  context?: LogContext
) {
  log.info(message, createLogContext(serviceName, operation, context));
}

export function logError(
  serviceName: string,
  message: string,
  error: unknown,
  operation: LogOperations,
  context?: LogContext
) {
  log.error(message, error, createLogContext(serviceName, operation, context));
}

export function logWarn(
  serviceName: string,
  message: string,
  operation: LogOperations,
  context?: LogContext
) {
  log.warn(message, createLogContext(serviceName, operation, context));
}

export function logDebug(
  serviceName: string,
  message: string,
  operation: LogOperations,
  context?: LogContext
) {
  log.debug(message, createLogContext(serviceName, operation, context));
}
