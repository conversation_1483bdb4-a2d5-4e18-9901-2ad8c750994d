import {
  OrderEntity,
  GiftStatus,
} from "../../../mikerudenko/marketplace-shared";
import { getGiftById } from "./gift-service";
import { log } from "../../../utils/logger";

// TODO here the logic is wrong
export async function processCancelledOrdersWithGifts(
  cancelledOrders: OrderEntity[]
): Promise<OrderEntity[]> {
  const cancelledOrdersWithGifts: OrderEntity[] = [];

  for (const order of cancelledOrders) {
    if (order.giftId && !order.buyerId) {
      try {
        const gift = await getGiftById(order.giftId);

        if (gift && gift.status === GiftStatus.DEPOSITED) {
          cancelledOrdersWithGifts.push(order);
        }
      } catch (error) {
        log.error(
          `Error fetching gift ${order.giftId} for order ${order.id}`,
          error,
          {
            operation: "process_cancelled_orders_with_gifts",
            orderId: order.id,
            giftId: order.giftId,
          }
        );
      }
    }
  }

  log.info("Processed cancelled orders with gifts", {
    operation: "process_cancelled_orders_with_gifts",
    totalCancelledOrders: cancelledOrders.length,
    cancelledOrdersWithGifts: cancelledOrdersWithGifts.length,
  });

  return cancelledOrdersWithGifts;
}
