import * as admin from "firebase-admin";
import {
  AppDate,
  formatDateToFirebaseTimestamp,
  OrderEntity,
  OrderStatus,
} from "../mikerudenko/marketplace-shared";
import { transferNetAmountToSeller } from "./purchase-fee-processing-service";
import { bpsToDecimal, safeMultiply } from "../utils";
import {
  sendNotificationSafely,
  notifyBuyerGiftSent,
} from "./notification-service";

export interface ProcessPaidOrderWithGiftParams {
  order: OrderEntity;
  giftId: string;
  orderDoc: admin.firestore.DocumentSnapshot;
  batch?: admin.firestore.WriteBatch;
  additionalOrderUpdates?: Record<string, any>;
}

/**
 * Reusable function to process paid orders when a gift is attached
 * This handles:
 * 1. Updating order status to GIFT_SENT_TO_RELAYER
 * 2. Attaching gift ID to order
 * 3. Calculating and transferring net amount to seller
 * 4. Committing the batch (if provided) or creating a new one
 */
export async function processPaidOrderWithGift({
  order,
  giftId,
  orderDoc,
  batch: providedBatch,
  additionalOrderUpdates = {},
}: ProcessPaidOrderWithGiftParams): Promise<void> {
  const db = admin.firestore();
  const batch = providedBatch || db.batch();
  const shouldCommit = !providedBatch;

  const orderUpdates = {
    giftId,
    status: OrderStatus.GIFT_SENT_TO_RELAYER,
    updatedAt: formatDateToFirebaseTimestamp(
      admin.firestore.Timestamp.now() as AppDate
    ),
    ...additionalOrderUpdates,
  };

  batch.update(orderDoc.ref, orderUpdates);

  // Transfer funds from buyer's locked balance to seller
  if (order.buyerId && order.sellerId) {
    // Calculate net amount using order's fee structure
    const purchaseFeeRate = order.fees?.purchase_fee || 0;
    const netSellerAmount = safeMultiply(
      order.price,
      1 - bpsToDecimal(purchaseFeeRate)
    );

    // Use reusable function for money transfer
    await transferNetAmountToSeller(order, netSellerAmount, order.buyerId);
  }

  // Commit batch if we created it
  if (shouldCommit) {
    await batch.commit();
  }

  // Send notification to buyer when gift is sent to relayer
  if (order.buyerId) {
    sendNotificationSafely(
      notifyBuyerGiftSent,
      {
        orderId: order.id!,
        buyerId: order.buyerId,
        orderNumber: order.number,
      },
      "order-gift-processing-service"
    );
  }
}

export function calculateNetSellerAmount(order: OrderEntity): number {
  const purchaseFeeRate = order.fees?.purchase_fee || 0;
  return safeMultiply(order.price, 1 - bpsToDecimal(purchaseFeeRate));
}
