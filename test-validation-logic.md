# Order Creation Validation Test

## Implementation Summary

The validation logic to prevent sellers from creating more than 3 orders with "created" status for market collections has been successfully implemented.

### Backend Implementation (marketplace-functions repository)

1. **Error Message Added** (`functions/src/error-messages.ts`):
   ```typescript
   TOO_MANY_CREATED_ORDERS: "errors.order.tooManyCreatedOrders",
   ```

2. **Validation Function** (`functions/src/services/order-validation-service.ts`):
   ```typescript
   export async function validateSellerCreatedOrdersLimit(
     db: admin.firestore.Firestore,
     sellerId: string,
     collectionId: string
   ): Promise<void> {
     // Get collection and check if it's MARKET status
     // Count existing orders with "created" status for this seller
     // Throw error if count >= 3
   }
   ```

3. **Integration** (`functions/src/order-functions/seller-order-function/seller-order-function.service.ts`):
   ```typescript
   static async createSellerOrder(params: CreateOrderAsSellerParams): Promise<any> {
     const db = admin.firestore();
     const { sellerId, collectionId, price } = params;

     // Validate that seller doesn't have too many orders with "created" status
     await validateSellerCreatedOrdersLimit(db, sellerId, collectionId);

     return await createOrder(db, { ... });
   }
   ```

### Frontend Implementation (marketplace-ui repository)

1. **Error Message Translation** (`src/components/app-intl/errors.messages.ts`):
   ```typescript
   'errors.order.tooManyCreatedOrders': {
     id: 'errors.order.tooManyCreatedOrders',
     defaultMessage: 'You already have 3 orders that need to be activated. Please activate existing orders before creating new ones.',
   },
   ```

2. **Error Handling** (`src/app/(app)/marketplace/create-order-drawer.tsx`):
   ```typescript
   try {
     const result = await createOrderFunction({ ... });
     toast.success(message);
   } catch (error: unknown) {
     const errorMessage = formatServerError(error, t);
     toast.error(errorMessage);
   }
   ```

## Validation Logic Details

### When Validation Applies
- Only for **MARKET** collections (not PREMARKET or DELETED)
- Only when creating **seller** orders (not buyer orders)
- Counts orders with status **"CREATED"** specifically

### Validation Process
1. Fetch collection document to verify it's a MARKET collection
2. Query orders collection for existing orders where:
   - `sellerId` equals the current seller
   - `status` equals `OrderStatus.CREATED`
3. If count >= 3, throw `HttpsError` with internationalized error key
4. Error propagates to frontend and displays localized message

### Error Flow
1. **Backend**: Validation fails → `HttpsError` thrown with JSON error response
2. **Frontend**: Error caught → `formatServerError()` parses JSON → Localized message displayed as toast

## Testing Scenarios

### Scenario 1: Valid Order Creation
- Seller has 0-2 orders with "created" status
- **Expected**: Order creation succeeds

### Scenario 2: Too Many Created Orders
- Seller has 3+ orders with "created" status for MARKET collection
- **Expected**: Error message displayed: "You already have 3 orders that need to be activated. Please activate existing orders before creating new ones."

### Scenario 3: PREMARKET Collection
- Seller has 3+ orders with "created" status for PREMARKET collection
- **Expected**: Order creation succeeds (validation only applies to MARKET collections)

## Implementation Benefits

1. **Database Flood Prevention**: Prevents sellers from creating unlimited unactivated orders
2. **User Experience**: Clear error message guides users to activate existing orders
3. **Internationalization**: Error message supports multiple languages
4. **Selective Application**: Only applies to MARKET collections where activation is required
5. **Maintainable**: Clean separation between validation logic and business logic

## Next Steps

The implementation is complete and ready for testing. To verify:

1. Deploy functions to development environment
2. Test order creation with different scenarios
3. Verify error messages display correctly in UI
4. Test with different collection statuses (MARKET vs PREMARKET)
