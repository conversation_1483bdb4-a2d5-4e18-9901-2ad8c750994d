import dotenv from "dotenv";
import path from "path";
import fs from "fs";

export function loadEnvironment() {
  const nodeEnv = process.env.NODE_ENV ?? "development";

  let envFile: string;
  if (nodeEnv === "development") {
    const localEnvPath = path.resolve(process.cwd(), ".env.local");
    if (fs.existsSync(localEnvPath)) {
      envFile = ".env.local";
    } else {
      envFile = ".env.development";
    }
  } else if (nodeEnv === "production") {
    envFile = ".env.production";
  } else {
    envFile = ".env.development";
  }

  console.log(`Loading environment file: ${envFile}`);

  const envPath = path.resolve(process.cwd(), envFile);

  const result = dotenv.config({ path: envPath });

  if (result.error) {
    console.warn(
      `⚠️ Warning: Could not load ${envFile}. Using default environment variables.`,
      result.error.message
    );
  } else {
    console.log(`✅ Loaded environment from ${envFile}`);
  }

  return result;
}
