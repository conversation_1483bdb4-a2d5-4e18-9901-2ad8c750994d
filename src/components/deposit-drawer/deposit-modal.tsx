'use client';

import confetti from 'canvas-confetti';
import { CheckCircle, Clock, Copy, ExternalLink, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';
import { Drawer } from 'vaul';

import { Button } from '@/components/ui/button';
import { useVisualViewport } from '@/hooks/use-visual-viewport';

import { depositDrawerMessages } from './intl/deposit-drawer.messages';

interface DepositModalProps {
  show: boolean;
  onClose: () => void;
  onComplete: () => void;
  initialSeconds?: number;
  title?: string;
  message?: string;
  onInterval?: (currentTime: number) => void;
  transactionHash?: string;
  isSuccess?: boolean;
}

export function DepositModal({
  show,
  onClose,
  onComplete,
  initialSeconds = 60,
  title,
  message,
  onInterval,
  transactionHash,
  isSuccess = false,
}: DepositModalProps) {
  const { formatMessage: t } = useIntl();
  const [countdown, setCountdown] = useState(initialSeconds);
  const [hasTriggeredConfetti, setHasTriggeredConfetti] = useState(false);
  const drawerContentRef = useVisualViewport({ enabled: show, offset: 64 });

  const defaultTitle = title || t(depositDrawerMessages.depositProcessing);
  const defaultMessage =
    message || t(depositDrawerMessages.youWillReceiveFundsWithin);

  useEffect(() => {
    if (show) {
      setCountdown(initialSeconds);
      setHasTriggeredConfetti(false);
    }
  }, [show, initialSeconds]);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (show && countdown > 0 && !isSuccess) {
      interval = setInterval(() => {
        setCountdown((prev) => {
          const newTime = prev - 1;
          onInterval?.(newTime);
          return newTime;
        });
      }, 1000);
    } else if (countdown === 0 && show && !isSuccess) {
      onComplete();
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [show, countdown, onComplete, onInterval, isSuccess]);

  useEffect(() => {
    if (isSuccess && !hasTriggeredConfetti) {
      triggerConfetti();
      setHasTriggeredConfetti(true);
    }
  }, [isSuccess, hasTriggeredConfetti]);

  const triggerConfetti = () => {
    const scalar = 2;
    const diamond = confetti.shapeFromText({ text: '💎', scalar });

    confetti({
      shapes: [diamond],
      scalar,
      particleCount: 100,
      spread: 160,
      origin: { y: 0.6 },
      startVelocity: 30,
    });

    setTimeout(() => {
      confetti({
        shapes: [diamond],
        scalar,
        particleCount: 50,
        spread: 120,
        origin: { x: 0.2, y: 0.7 },
        startVelocity: 25,
      });
    }, 250);

    setTimeout(() => {
      confetti({
        shapes: [diamond],
        scalar,
        particleCount: 50,
        spread: 120,
        origin: { x: 0.8, y: 0.7 },
        startVelocity: 25,
      });
    }, 500);
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleCopyTransactionHash = async () => {
    if (transactionHash) {
      try {
        await navigator.clipboard.writeText(transactionHash);
        toast.success(t(depositDrawerMessages.transactionHashCopied));
      } catch (error) {
        console.error('Failed to copy transaction hash:', error);
        toast.error('Failed to copy transaction hash');
      }
    }
  };

  const handleViewOnTonScan = () => {
    if (transactionHash) {
      window.open(`https://tonscan.org/tx/${transactionHash}`, '_blank');
    }
  };

  if (!show) return null;

  return (
    <Drawer.Root
      open={show}
      onOpenChange={(open) => !open && onClose()}
      shouldScaleBackground
      modal
      dismissible={isSuccess}
    >
      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
        <Drawer.Content
          ref={drawerContentRef}
          className="bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[101] outline-none focus:outline-none max-h-[90vh]"
        >
          <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

            <div className="max-w-md mx-auto space-y-6">
              <div className="flex items-center gap-3 mb-6">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    isSuccess ? 'bg-green-500' : 'bg-[#6ab2f2]'
                  }`}
                >
                  {isSuccess ? (
                    <CheckCircle className="w-5 h-5 text-white" />
                  ) : (
                    <Clock className="w-5 h-5 text-white" />
                  )}
                </div>
                <div className="flex-1">
                  <h2 className="text-xl font-semibold text-[#f5f5f5]">
                    {isSuccess
                      ? t(depositDrawerMessages.depositSuccess)
                      : defaultTitle}
                  </h2>
                  <p className="text-sm text-[#708499] mt-1">
                    {isSuccess
                      ? t(depositDrawerMessages.depositCompleted)
                      : `${defaultMessage} ${formatTime(countdown)}`}
                  </p>
                </div>
                {isSuccess && (
                  <button
                    onClick={onClose}
                    className="flex-shrink-0 text-[#708499] hover:text-[#f5f5f5] transition-colors"
                    aria-label={t(depositDrawerMessages.close)}
                  >
                    <X className="w-5 h-5" />
                  </button>
                )}
              </div>

              {isSuccess && transactionHash && (
                <div className="space-y-3">
                  <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
                    <div className="space-y-3">
                      <Button
                        onClick={handleViewOnTonScan}
                        className="w-full bg-[#6ab2f2] hover:bg-[#5a9fd9] text-white"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        {t(depositDrawerMessages.viewOnTonScan)}
                      </Button>

                      <Button
                        onClick={handleCopyTransactionHash}
                        variant="outline"
                        className="w-full border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#232e3c]"
                      >
                        <Copy className="w-4 h-4 mr-2" />
                        {t(depositDrawerMessages.copyTransactionHash)}
                      </Button>
                    </div>
                  </div>

                  <Button
                    onClick={onClose}
                    className="w-full bg-[#232e3c] hover:bg-[#2a3441] text-[#f5f5f5]"
                  >
                    {t(depositDrawerMessages.close)}
                  </Button>
                </div>
              )}

              {!isSuccess && (
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#6ab2f2] mx-auto mb-2"></div>
                  <p className="text-sm text-[#708499]">
                    {t(depositDrawerMessages.processingYourDeposit)}
                  </p>
                </div>
              )}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
