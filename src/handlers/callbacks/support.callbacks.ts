import { Context } from "telegraf";
import {
  createSupportKeyboard,
  createMarketplaceInlineKeyboard,
} from "../../utils/keyboards";
import { T } from "../../i18n";
import { botMessages } from "../../intl/messages";
import { PREM_SUPPORT_OFFICIAL } from "../../app.constants";

export const handleOrderHelpCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(T(ctx, botMessages.orderHelp.id), createSupportKeyboard(ctx));
};

export const handleContactSupportCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    T(ctx, botMessages.callbackContactSupport.id, { PREM_SUPPORT_OFFICIAL }),
    createMarketplaceInlineKeyboard(ctx)
  );
};
