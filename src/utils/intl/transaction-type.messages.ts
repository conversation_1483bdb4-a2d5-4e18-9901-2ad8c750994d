import { defineMessages } from 'react-intl';

export const transactionTypeMessages = defineMessages({
  deposit: {
    id: 'transaction.type.deposit',
    defaultMessage: 'Deposit',
  },
  withdraw: {
    id: 'transaction.type.withdraw',
    defaultMessage: 'Withdraw',
  },
  buyLockCollateral: {
    id: 'transaction.type.buyLockCollateral',
    defaultMessage: 'Buy Lock',
  },
  unlockCollateral: {
    id: 'transaction.type.unlockCollateral',
    defaultMessage: 'Unlock',
  },
  sellLockCollateral: {
    id: 'transaction.type.sellLockCollateral',
    defaultMessage: 'Sell Lock',
  },
  referralFee: {
    id: 'transaction.type.referralFee',
    defaultMessage: 'Referral Fee',
  },
  cancelationFee: {
    id: 'transaction.type.cancelationFee',
    defaultMessage: 'Cancel Fee',
  },
  refund: {
    id: 'transaction.type.refund',
    defaultMessage: 'Refund',
  },
  sellFulfillment: {
    id: 'transaction.type.sellFulfillment',
    defaultMessage: 'Fulfillment',
  },
  resellFeeEarnings: {
    id: 'transaction.type.resellFeeEarnings',
    defaultMessage: 'Resell Earnings',
  },
});
