import { zodResolver } from '@hookform/resolvers/zod';
import { AlertTriangle } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { updateUserReferralFee } from '@/api/admin-api';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { NumberInput } from '@/components/ui/number-input';
import { useToast } from '@/hooks/use-toast';
import type { UserEntity } from '@/mikerudenko/marketplace-shared';

import {
  type CustomReferralFormData,
  customReferralSchema,
  type UserWithCustomReferral,
} from './custom-referral-types';
import { getCustomReferralWarnings } from './custom-referral-utils';
import { EnhancedTelegramUserSearch } from './enhanced-telegram-user-search';

interface CustomReferralDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  maxPurchaseFee: number;
  editingUser?: UserWithCustomReferral | null;
}

export const ManageReferralDialog = ({
  isOpen,
  onClose,
  onSuccess,
  maxPurchaseFee,
  editingUser,
}: CustomReferralDialogProps) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [telegramHandleSearch, setTelegramHandleSearch] = useState('');
  const [selectedUser, setSelectedUser] = useState<UserEntity | null>(null);

  const isEditMode = !!editingUser;

  const form = useForm<CustomReferralFormData>({
    resolver: zodResolver(customReferralSchema),
    defaultValues: {
      telegramHandle: '',
      referralFee: 0,
    },
    mode: 'onChange',
  });

  const referralFee = form.watch('referralFee');
  const warnings = getCustomReferralWarnings(referralFee, maxPurchaseFee);

  const resetForm = useCallback(() => {
    setTelegramHandleSearch('');
    setSelectedUser(null);
    form.reset({
      telegramHandle: '',
      referralFee: 0,
    });
  }, [form]);

  // Initialize form for edit mode
  useEffect(() => {
    if (!isOpen) return;

    if (isEditMode && editingUser) {
      const userEntity: UserEntity = {
        id: editingUser.id,
        displayName: editingUser.displayName,
        telegram_handle: editingUser.telegram_handle,
      } as UserEntity;

      setSelectedUser(userEntity);
      setTelegramHandleSearch(editingUser.telegram_handle || '');
      form.reset({
        telegramHandle: editingUser.telegram_handle || '',
        referralFee: editingUser.referral_fee,
      });
    } else if (!isEditMode) {
      resetForm();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, isEditMode, editingUser]);

  const handleUserSelect = (user: UserEntity | null) => {
    setSelectedUser(user);
    if (user) {
      form.setValue('telegramHandle', user.telegram_handle || '');
    } else {
      form.setValue('telegramHandle', '');
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const onSubmit = async (data: CustomReferralFormData) => {
    // For edit mode, we already have the user
    const targetUser = isEditMode ? editingUser : selectedUser;

    if (!targetUser) {
      toast({
        title: 'Error',
        description: isEditMode
          ? 'No user to edit'
          : 'Please select a user first',
        variant: 'destructive',
      });
      return;
    }

    // Additional validation for add mode
    if (!isEditMode && (!data.telegramHandle || !data.telegramHandle.trim())) {
      toast({
        title: 'Error',
        description: 'Please select a user first',
        variant: 'destructive',
      });
      return;
    }

    if (data.referralFee > maxPurchaseFee) {
      toast({
        title: 'Error',
        description: `Referral fee cannot be more than purchase fee (${maxPurchaseFee} BPS)`,
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);

      await updateUserReferralFee(targetUser.id, data.referralFee);

      toast({
        title: 'Success',
        description: `Custom referral fee ${isEditMode ? 'updated' : 'set'} for ${targetUser.displayName || targetUser.telegram_handle}`,
      });

      resetForm();
      onSuccess();
      onClose();
    } catch (error) {
      console.error(
        `Error ${isEditMode ? 'updating' : 'setting'} custom referral fee:`,
        error,
      );
      toast({
        title: 'Error',
        description: `Failed to ${isEditMode ? 'update' : 'set'} custom referral fee`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {isEditMode
              ? 'Edit Custom Referral Fee'
              : 'Set Custom Referral Fee'}
          </DialogTitle>
          <DialogDescription>
            {isEditMode
              ? `Update the custom referral fee for ${editingUser?.displayName || editingUser?.telegram_handle || 'this user'}.`
              : 'Set a custom referral fee for a specific user. The fee cannot exceed the purchase fee (' +
                maxPurchaseFee +
                ' BPS).'}{' '}
            Note: Referral fees only apply to purchase fees, not resell fees.
          </DialogDescription>
        </DialogHeader>

        {warnings.length > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Configuration Warnings</AlertTitle>
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {warnings.map((warning) => (
                  <li key={warning} className="text-sm">
                    {warning}
                  </li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {!isEditMode && (
              <EnhancedTelegramUserSearch
                value={telegramHandleSearch}
                onUserSelect={handleUserSelect}
                onSearchChange={setTelegramHandleSearch}
                selectedUser={selectedUser}
              />
            )}

            {isEditMode && editingUser && (
              <div className="flex items-center gap-3 p-3 bg-[#232e3c] border border-[#3a4a5c] rounded-lg">
                <div className="flex-1">
                  <div className="font-medium text-white">
                    @{editingUser.telegram_handle}
                  </div>
                  <div className="text-sm text-[#708499]">
                    {editingUser.displayName || 'No display name'}
                  </div>
                </div>
              </div>
            )}

            <FormField
              control={form.control}
              name="referralFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Referral Fee (BPS)</FormLabel>
                  <FormControl>
                    <NumberInput
                      placeholder="200"
                      value={field.value || 0}
                      onValueChange={(value) => {
                        const numValue = value ? Number(value) : 0;
                        field.onChange(numValue);
                      }}
                      min={0}
                      max={10000}
                      step={1}
                      disabled={!isEditMode && !selectedUser}
                    />
                  </FormControl>
                  <FormDescription>
                    Referral fee in basis points (BPS). 100 BPS = 1%. Only
                    applies to purchase fees, not resell fees.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading || (!isEditMode && !selectedUser)}
              >
                {isLoading
                  ? isEditMode
                    ? 'Updating...'
                    : 'Setting...'
                  : isEditMode
                    ? 'Update Fee'
                    : 'Set Custom Fee'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

// Backward compatibility exports
export const AddCustomReferralDialog = ManageReferralDialog;
export const EditCustomReferralDialog = ManageReferralDialog;
