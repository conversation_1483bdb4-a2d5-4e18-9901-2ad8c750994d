import { HealthcheckLogger } from "./healthcheck.logger";

export class HealthcheckService {
  private static botStartTime: Date = new Date();

  static getBotStartTime(): Date {
    return this.botStartTime;
  }

  static setBotStartTime(): void {
    this.botStartTime = new Date();
    HealthcheckLogger.logBotStartTimeUpdated({
      timestamp: this.botStartTime.toISOString(),
    });
  }

  static async getLastHealthcheck(): Promise<string | null> {
    // Return bot start time as the last healthcheck
    return this.botStartTime.toISOString();
  }

  static async isHealthy(): Promise<boolean> {
    try {
      const now = new Date();
      const timeDiff = now.getTime() - this.botStartTime.getTime();
      const hoursDiff = timeDiff / (1000 * 60 * 60);

      // Consider healthy if bot has been running for less than 24 hours without restart
      // This is a simple health check based on uptime
      return hoursDiff < 24;
    } catch (error) {
      HealthcheckLogger.logHealthStatusCheckError({
        error,
      });
      return false;
    }
  }
}
