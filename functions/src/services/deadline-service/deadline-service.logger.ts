import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const deadlineServiceLogger = createServiceLogger("deadline-service");

export const DeadlineServiceLogger = {
  logDeadlineAdded({
    orderId,
    collectionId,
    deadline,
  }: {
    orderId: string;
    collectionId: string;
    deadline: string;
  }) {
    deadlineServiceLogger.logInfo(
      "Added dynamic deadline to order for MARKET collection",
      LogOperations.DEADLINE_ADDED,
      {
        orderId,
        collectionId,
        deadline,
      }
    );
  },

  logNoDeadlineUpdatesNeeded({ collectionId }: { collectionId: string }) {
    deadlineServiceLogger.logInfo(
      "No orders need deadline updates for collection",
      LogOperations.NO_UPDATES_NEEDED,
      {
        collectionId,
      }
    );
  },

  logDeadlineBatchProcessed({
    batchNumber,
    updatedCount,
    collectionId,
  }: {
    batchNumber: number;
    updatedCount: number;
    collectionId: string;
  }) {
    deadlineServiceLogger.logInfo(
      "Processed batch for deadline updates",
      LogOperations.BATCH_PROCESSED,
      {
        batchNumber,
        updatedCount,
        collectionId,
      }
    );
  },

  logDeadlineUpdatesCompleted({
    totalUpdatedCount,
    collectionId,
  }: {
    totalUpdatedCount: number;
    collectionId: string;
  }) {
    deadlineServiceLogger.logInfo(
      "Added dynamic deadlines to orders for collection",
      LogOperations.DEADLINES_COMPLETED,
      {
        totalUpdatedCount,
        collectionId,
      }
    );
  },

  logDeadlineServiceError({
    error,
    operation,
    orderId,
    collectionId,
  }: {
    error: unknown;
    operation: LogOperations;
    orderId?: string;
    collectionId?: string;
  }) {
    deadlineServiceLogger.logError(
      `Error in deadline service: ${operation}`,
      error,
      operation,
      {
        orderId,
        collectionId,
      }
    );
  },
};
