import { createServiceLogger } from "../logger/logger";
import { LogOperations } from "../constants";

const txLookupLogger = createServiceLogger("tx-lookup-function");

export const TxLookupLogger = {
  logTxLookupGetError(data: { error: unknown }) {
    txLookupLogger.logError(
      "Error getting tx lookup",
      data.error,
      LogOperations.GET_TX_LOOKUP,
      {}
    );
  },

  logTxLookupUpdated(data: { lastCheckedRecordId: string }) {
    txLookupLogger.logInfo(
      `Updated tx lookup with last_checked_record_id: ${data.lastCheckedRecordId}`,
      LogOperations.UPDATE_TX_LOOKUP,
      {
        lastCheckedRecordId: data.lastCheckedRecordId,
      }
    );
  },

  logTxLookupUpdateError(data: {
    error: unknown;
    lastCheckedRecordId: string;
  }) {
    txLookupLogger.logError(
      "Error updating tx lookup",
      data.error,
      LogOperations.UPDATE_TX_LOOKUP,
      {
        lastCheckedRecordId: data.lastCheckedRecordId,
      }
    );
  },
};
