import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const fulfillAndResellLogger = createServiceLogger(
  "fulfill-and-resell-function"
);

export const FulfillAndResellLogger = {
  logFulfillAndResellStarted({
    orderId,
    userId,
    price,
  }: {
    orderId: string;
    userId: string;
    price: number;
  }) {
    fulfillAndResellLogger.logInfo(
      "Fulfill and resell order started",
      LogOperations.FULFILL_AND_RESELL,
      {
        orderId,
        userId,
        price,
      }
    );
  },

  logFulfillAndResellSuccess({
    originalOrderId,
    newOrderId,
    userId,
    price,
    lockAmount,
  }: {
    originalOrderId: string;
    newOrderId: string;
    userId: string;
    price: number;
    lockAmount: number;
  }) {
    fulfillAndResellLogger.logInfo(
      "Order fulfilled and resell order created successfully",
      LogOperations.FULFILL_AND_RESELL,
      {
        originalOrderId,
        newOrderId,
        userId,
        price,
        lockAmount,
      }
    );
  },

  logFulfillAndResellError({
    error,
    orderId,
    userId,
    price,
  }: {
    error: unknown;
    orderId?: string;
    userId?: string;
    price?: number;
  }) {
    fulfillAndResellLogger.logError(
      "Error fulfilling order and creating resell order",
      error,
      LogOperations.FULFILL_AND_RESELL,
      {
        orderId,
        userId,
        price,
      }
    );
  },
};
