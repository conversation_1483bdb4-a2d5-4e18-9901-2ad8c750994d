import { defineMessages } from "@formatjs/intl";

export const commandsMessages = defineMessages({
  startCommandDescription: {
    id: "commands.start.description",
    defaultMessage: "Start the bot and show main menu",
  },
  helpCommandDescription: {
    id: "commands.help.description",
    defaultMessage: "Show help information",
  },
  healthCommandDescription: {
    id: "commands.health.description",
    defaultMessage: "Check bot health status",
  },
  healthStatusHealthy: {
    id: "commands.health.statusHealthy",
    defaultMessage: "✅ Healthy",
  },
  healthStatusUnhealthy: {
    id: "commands.health.statusUnhealthy",
    defaultMessage: "⚠️ Unhealthy",
  },
  healthLastCheck: {
    id: "commands.health.lastCheck",
    defaultMessage: "{status}\n\nLast healthcheck: {timestamp}",
  },
  healthNoData: {
    id: "commands.health.noData",
    defaultMessage: "❌ No healthcheck data found",
  },
  healthError: {
    id: "commands.health.error",
    defaultMessage: "❌ Error checking health status",
  },
});
