'use client';

import { useAdminAuth } from '@/hooks/use-admin-auth';

import { AppConfigManagement } from './app-config-management';

export default function AppConfigPage() {
  const { isAuthorized } = useAdminAuth();

  if (!isAuthorized) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">App Configuration</h1>
        <p className="text-muted-foreground">
          Configure marketplace fees, limits, and settings
        </p>
      </div>

      <AppConfigManagement />
    </div>
  );
}
