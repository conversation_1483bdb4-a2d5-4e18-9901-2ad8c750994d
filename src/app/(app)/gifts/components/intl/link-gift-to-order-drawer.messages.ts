import { defineMessages } from 'react-intl';

export const linkGiftToOrderDrawerMessages = defineMessages({
  linkGiftToOrder: {
    id: 'linkGiftToOrderDrawer.linkGiftToOrder',
    defaultMessage: 'Link Gift to Order',
  },
  selectOrderToLink: {
    id: 'linkGiftToOrderDrawer.selectOrderToLink',
    defaultMessage:
      'Select an order from {collectionName} collection to link this gift',
  },
  noEligibleOrders: {
    id: 'linkGiftToOrderDrawer.noEligibleOrders',
    defaultMessage:
      'No eligible orders found. You need orders with status "Created" or "Paid" where you are the seller.',
  },
  cancel: {
    id: 'linkGiftToOrderDrawer.cancel',
    defaultMessage: 'Cancel',
  },
  linking: {
    id: 'linkGiftToOrderDrawer.linking',
    defaultMessage: 'Linking...',
  },
});
