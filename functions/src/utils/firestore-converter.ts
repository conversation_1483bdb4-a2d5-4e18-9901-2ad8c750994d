import * as admin from "firebase-admin";

export function convertDocsToEntities<T>(
  docs: admin.firestore.QueryDocumentSnapshot[]
): (T & { id: string })[] {
  return docs.map(
    (doc) => ({ id: doc.id, ...doc.data() } as T & { id: string })
  );
}

export function convertDocToEntity<T>(
  doc: admin.firestore.DocumentSnapshot
): (T & { id: string }) | null {
  if (!doc.exists) {
    return null;
  }
  return { id: doc.id, ...doc.data() } as T & { id: string };
}
