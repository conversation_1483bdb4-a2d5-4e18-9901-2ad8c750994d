import { createServiceLogger } from "../logger/base-logger";
import { LogOperations } from "../constants";

// Create service logger using functional composition
const utilsLogger = createServiceLogger("utils");

export const UtilsLogger = {
  logDivisionByZeroError({
    amount,
    divisor,
  }: {
    amount: number;
    divisor: number;
  }) {
    utilsLogger.logError(
      "Division by zero error in safeDivide function",
      new Error("Division by zero"),
      LogOperations.SAFE_DIVIDE,
      {
        amount,
        divisor,
      }
    );
  },
};
