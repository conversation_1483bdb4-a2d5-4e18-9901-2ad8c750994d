import * as admin from "firebase-admin";
import { CallableRequest, HttpsError } from "firebase-functions/v2/https";
import { AUTH_ERRORS, VALIDATION_ERRORS } from "../error-messages";
import {
  APP_USERS_COLLECTION,
  UserEntity,
  UserType,
} from "../mikerudenko/marketplace-shared";

export interface AuthenticatedRequest extends CallableRequest {
  auth: NonNullable<CallableRequest["auth"]>;
}

export interface ValidatedUserRequest extends AuthenticatedRequest {
  user: UserEntity;
}

export function requireAuthentication(request: CallableRequest) {
  if (!request.auth) {
    throw new HttpsError(
      "unauthenticated",
      JSON.stringify({
        errorKey: AUTH_ERRORS.UNAUTHENTICATED,
        fallbackMessage: "Authentication required.",
      })
    );
  }
  return request as AuthenticatedRequest;
}

export function requireUserPermission(params: {
  request: AuthenticatedRequest;
  userId: string;
  operation?: string;
}) {
  const { request, userId, operation = "operation" } = params;
  if (request.auth.uid !== userId) {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: AUTH_ERRORS.PERMISSION_DENIED_WITH_OPERATION,
        params: { operation },
        fallbackMessage: `You can only perform ${operation} for yourself.`,
      })
    );
  }
}

export function validateRequiredParams(params: {
  data: any;
  requiredParams: string[];
}) {
  const { data, requiredParams } = params;
  for (const param of requiredParams) {
    if (!data[param]) {
      throw new HttpsError(
        "invalid-argument",
        JSON.stringify({
          errorKey: VALIDATION_ERRORS.REQUIRED_FIELD,
          params: { field: param },
          fallbackMessage: `${param} is required.`,
        })
      );
    }
  }
}

export function validatePositiveAmount(params: {
  amount: number;
  fieldName?: string;
}) {
  const { amount, fieldName = "amount" } = params;
  if (!amount || amount <= 0) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.POSITIVE_AMOUNT_REQUIRED,
        params: { fieldName },
        fallbackMessage: `${fieldName} must be greater than 0.`,
      })
    );
  }
}

export async function getUserData(userId: string) {
  const db = admin.firestore();
  const userDoc = await db.collection(APP_USERS_COLLECTION).doc(userId).get();

  if (!userDoc.exists) {
    throw new HttpsError(
      "not-found",
      JSON.stringify({
        errorKey: AUTH_ERRORS.USER_NOT_FOUND,
        fallbackMessage: "User not found.",
      })
    );
  }

  return { id: userDoc.id, ...userDoc.data() } as UserEntity;
}

export async function requireAdminRole(userId: string) {
  const user = await getUserData(userId);

  if (user.role !== "admin") {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: AUTH_ERRORS.ADMIN_ONLY,
        fallbackMessage: "Only admin users can perform this operation.",
      })
    );
  }

  return user;
}

export function requireTonWallet(user: UserEntity) {
  if (!user.ton_wallet_address) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: AUTH_ERRORS.TON_WALLET_REQUIRED,
        fallbackMessage: "User does not have a TON wallet address configured.",
      })
    );
  }
}

export async function authenticateAndGetUser(request: CallableRequest) {
  const authRequest = requireAuthentication(request);
  const user = await getUserData(authRequest.auth.uid);

  return { request: authRequest, user };
}

export function validateBuyerOwnership(
  request: AuthenticatedRequest,
  buyerId: string
) {
  requireUserPermission({
    request,
    userId: buyerId,
    operation: "buyer operations",
  });
}

export function validateSellerOwnership(
  request: AuthenticatedRequest,
  sellerId: string
) {
  requireUserPermission({
    request,
    userId: sellerId,
    operation: "seller operations",
  });
}

export function validateOrderCreationParams(
  data: {
    sellerId?: string;
    buyerId?: string;
    collectionId: string;
    price: number;
  },
  userType: UserType
) {
  const userIdField = userType === UserType.BUYER ? "buyerId" : "sellerId";

  validateRequiredParams({
    data,
    requiredParams: [userIdField, "collectionId", "price"],
  });
  validatePositiveAmount({ amount: data.price });
}

export function validatePurchaseParams(
  data: Record<string, any>,
  userType: UserType
) {
  const userIdField = userType === UserType.BUYER ? "buyerId" : "sellerId";

  validateRequiredParams({
    data,
    requiredParams: [userIdField, "orderId"],
  });
}
