import { HttpsError, onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../constants";
import {
  getUserData,
  requireAuthentication,
} from "../services/auth-middleware";
import { validateTelegramIdForOrderOperation } from "../services/telegram-validation-service";
import {
  acceptProposal as acceptProposalService,
  cancelProposal as cancelProposalService,
} from "./proposal-service";

import { proposeOrderPrice as proposeOrderPriceService } from "./proposal-service";

export interface ProposeOrderPriceParams {
  orderId: string;
  proposedPrice: number;
}

export interface ProposeOrderPriceResult {
  success: boolean;
  message: string;
  proposalId?: string;
}

export interface AcceptProposalParams {
  orderId: string;
  proposalId: string;
}

export interface AcceptProposalResult {
  success: boolean;
  message: string;
  newOrderPrice?: number;
}

export const acceptProposal = onCall<AcceptProposalParams>(
  commonFunctionsConfig,
  async (request) => {
    const authRequest = requireAuthentication(request);
    const { orderId, proposalId } = request.data;
    const sellerId = authRequest.auth.uid;

    if (!orderId || !proposalId) {
      throw new HttpsError(
        "invalid-argument",
        "Order ID and proposal ID are required"
      );
    }

    try {
      // Validate user has telegram ID for proposal operations
      const user = await getUserData(sellerId);
      validateTelegramIdForOrderOperation(user, "accept price proposals");

      const result = await acceptProposalService(orderId, proposalId, sellerId);

      return result;
    } catch (error) {
      console.error("Error accepting proposal:", error);

      if (error instanceof HttpsError) {
        throw error;
      }

      throw new HttpsError("internal", "Failed to accept proposal");
    }
  }
);

export interface CancelProposalParams {
  orderId: string;
}

export interface CancelProposalResult {
  success: boolean;
  message: string;
  refundAmount?: number;
  feeAmount?: number;
}

export const cancelProposal = onCall<CancelProposalParams>(
  commonFunctionsConfig,
  async (request) => {
    const authRequest = requireAuthentication(request);
    const { orderId } = request.data;
    const proposerId = authRequest.auth.uid;

    if (!orderId) {
      throw new HttpsError("invalid-argument", "Order ID is required");
    }

    try {
      // Validate user has telegram ID for proposal operations
      const user = await getUserData(proposerId);
      validateTelegramIdForOrderOperation(user, "cancel price proposals");

      const result = await cancelProposalService(orderId, proposerId);

      return result;
    } catch (error) {
      console.error("Error cancelling proposal:", error);

      if (error instanceof HttpsError) {
        throw error;
      }

      throw new HttpsError("internal", "Failed to cancel proposal");
    }
  }
);

export const proposeOrderPrice = onCall<ProposeOrderPriceParams>(
  commonFunctionsConfig,
  async (request) => {
    const authRequest = requireAuthentication(request);
    const { orderId, proposedPrice } = request.data;
    const proposerId = authRequest.auth.uid;

    if (!orderId || !proposedPrice || proposedPrice <= 0) {
      throw new HttpsError(
        "invalid-argument",
        "Order ID and positive proposed price are required"
      );
    }

    try {
      // Validate user has telegram ID for proposal operations
      const user = await getUserData(proposerId);
      validateTelegramIdForOrderOperation(user, "create price proposals");

      const result = await proposeOrderPriceService(
        orderId,
        proposerId,
        proposedPrice
      );

      return result;
    } catch (error) {
      console.error("Error proposing order price:", error);

      if (error instanceof HttpsError) {
        throw error;
      }

      throw new HttpsError("internal", "Failed to propose order price");
    }
  }
);
