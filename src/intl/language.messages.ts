import { defineMessages } from "@formatjs/intl";

export const languageMessages = defineMessages({
  languageDetectionPromptUkrainian: {
    id: "language.detectionPromptUkrainian",
    defaultMessage:
      "🌍 We detected that you are using the app in Ukrainian language. Would you like to switch to Ukrainian?",
  },
  languageDetectionPromptRussian: {
    id: "language.detectionPromptRussian",
    defaultMessage:
      "🌍 We detected that you are using the app in Russian language. Would you like to switch to Russian?",
  },
  languageKeepEnglish: {
    id: "language.keepEnglish",
    defaultMessage: "No, I want to stay in English",
  },
  languageSwitchToUkrainian: {
    id: "language.switchToUkrainian",
    defaultMessage: "Yes, let's switch to Ukrainian",
  },
  languageSwitchToRussian: {
    id: "language.switchToRussian",
    defaultMessage: "Yes, let's switch to Russian",
  },
  languageSetToEnglish: {
    id: "language.setToEnglish",
    defaultMessage: "✅ Language set to English. Welcome to {APP_NAME} Bot!",
  },
  languageSetToUkrainian: {
    id: "language.setToUkrainian",
    defaultMessage:
      "✅ Мову змінено на українську. Ласкаво просимо до {APP_NAME} Bot!",
  },
  languageSetToRussian: {
    id: "language.setToRussian",
    defaultMessage:
      "✅ Язык изменен на русский. Добро пожаловать в {APP_NAME} Bot!",
  },
});
