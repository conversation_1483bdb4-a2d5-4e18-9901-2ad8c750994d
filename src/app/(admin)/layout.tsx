'use client';

import type { ReactNode } from 'react';
import { useEffect } from 'react';

import { AdminSidebar } from './admin/components/admin-sidebar';

export default function AdminLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  useEffect(() => {
    // Ensure dark mode is always enabled for admin pages
    if (typeof window !== 'undefined') {
      document?.documentElement?.classList.add('dark');
    }
  }, []);

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <main className="flex-1 overflow-auto">
        <div className="p-6">{children}</div>
      </main>
    </div>
  );
}
