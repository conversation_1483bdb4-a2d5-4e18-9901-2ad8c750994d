import bot from "../bot";
import { T } from "../i18n";
import { botMessages } from "../intl/messages";
import { PREM_RELAYER_USERNAME } from "../app.constants";
import { FirebaseUserService } from "./firebase-user.service";
import { BotService } from "./bot.service";
import { OrderService } from "./order.service";
import { NotificationServiceLogger } from "./notification-service.logger";

export interface NotifyBuyParams {
  orderId: string;
  sellerId: string;
  orderNumber?: number;
  price?: number;
}

export interface NotifyGiftSendParams {
  orderId: string;
  buyerId: string;
  orderNumber?: number;
}

export interface NotifyNewProposalParams {
  orderId: string;
  sellerId: string;
  proposedPrice: number;
  originalPrice: number;
  orderNumber?: number;
}

export interface NotifyProposalAcceptedParams {
  orderId: string;
  proposerId: string;
  proposedPrice: number;
  orderNumber?: number;
}

export async function notifySellerOrderPaid(
  params: NotifyBuyParams
): Promise<{ success: boolean; message: string }> {
  try {
    const { orderId, sellerId, orderNumber, price } = params;

    NotificationServiceLogger.logSellerNotificationStart({
      orderId,
      sellerId,
      ...(orderNumber !== undefined && { orderNumber }),
      ...(price !== undefined && { price }),
    });

    // Get user data from Firebase
    const userData = await FirebaseUserService.getUserById(sellerId);

    const botUsername = await BotService.getBotUsername();
    const orderLink = OrderService.createOrderDeepLink(botUsername, orderId);

    // Create context for translation
    const ctx = {
      userLanguage: userData.userLanguage || "en",
    };

    // Prepare message parameters
    const messageParams = {
      orderNumber: orderNumber || "N/A",
      price: price ? price.toString() : "N/A",
      orderLink,
    };

    const message = T(
      ctx,
      botMessages.sellerOrderPaidWithLink.id,
      messageParams
    );

    // Send notification to seller
    await bot.telegram.sendMessage(userData.tg_id, message, {
      parse_mode: "Markdown",
      link_preview_options: { is_disabled: true },
    });

    NotificationServiceLogger.logSellerNotificationSuccess({
      orderId,
      sellerId,
      tgId: userData.tg_id,
    });

    return {
      success: true,
      message: "Seller notification sent successfully",
    };
  } catch (error) {
    NotificationServiceLogger.logSellerNotificationError(error, {
      orderId: params.orderId,
      sellerId: params.sellerId,
    });

    return {
      success: false,
      message: `Failed to send notification: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

export async function notifyBuyerGiftSent(
  params: NotifyGiftSendParams
): Promise<{ success: boolean; message: string }> {
  try {
    const { orderId, buyerId, orderNumber } = params;

    NotificationServiceLogger.logBuyerNotificationStart({
      orderId,
      buyerId,
      ...(orderNumber !== undefined && { orderNumber }),
    });

    // Get user data from Firebase
    const userData = await FirebaseUserService.getUserById(buyerId);

    const botUsername = await BotService.getBotUsername();
    const orderLink = OrderService.createOrderDeepLink(botUsername, orderId);

    // Create context for translation
    const ctx = {
      userLanguage: userData.userLanguage || "en",
    };

    // Prepare message parameters
    const messageParams = {
      orderNumber: orderNumber || "N/A",
      relayerUsername: PREM_RELAYER_USERNAME.replace("@", ""),
      orderLink,
    };

    const message = T(ctx, botMessages.buyerGiftSentWithLink.id, messageParams);

    // Send notification to buyer
    await bot.telegram.sendMessage(userData.tg_id, message, {
      parse_mode: "Markdown",
      link_preview_options: { is_disabled: true },
    });

    NotificationServiceLogger.logBuyerNotificationSuccess({
      orderId,
      buyerId,
      tgId: userData.tg_id,
    });

    return {
      success: true,
      message: "Buyer notification sent successfully",
    };
  } catch (error) {
    NotificationServiceLogger.logBuyerNotificationError(error, {
      orderId: params.orderId,
      buyerId: params.buyerId,
    });

    return {
      success: false,
      message: `Failed to send notification: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

export async function notifySellerNewProposal(
  params: NotifyNewProposalParams
): Promise<{ success: boolean; message: string }> {
  try {
    const { orderId, sellerId, proposedPrice, originalPrice, orderNumber } =
      params;

    NotificationServiceLogger.logSellerNotificationStart({
      orderId,
      sellerId,
      ...(orderNumber !== undefined && { orderNumber }),
      proposedPrice,
      originalPrice,
    });

    // Get user data from Firebase
    const userData = await FirebaseUserService.getUserById(sellerId);

    const botUsername = await BotService.getBotUsername();
    const orderLink = OrderService.createOrderDeepLink(botUsername, orderId);

    // Create context for translation
    const ctx = {
      userLanguage: userData.userLanguage || "en",
    };

    // Prepare message parameters
    const messageParams = {
      orderNumber: orderNumber || "N/A",
      proposedPrice: proposedPrice.toString(),
      originalPrice: originalPrice.toString(),
      orderLink,
    };

    const message = T(ctx, botMessages.newProposalReceived.id, messageParams);

    // Send notification to seller
    await bot.telegram.sendMessage(userData.tg_id, message, {
      parse_mode: "Markdown",
      link_preview_options: { is_disabled: true },
    });

    NotificationServiceLogger.logSellerNotificationSuccess({
      orderId,
      sellerId,
      tgId: userData.tg_id,
    });

    return {
      success: true,
      message: "New proposal notification sent successfully",
    };
  } catch (error) {
    NotificationServiceLogger.logSellerNotificationError(error, {
      orderId: params.orderId,
      sellerId: params.sellerId,
    });

    return {
      success: false,
      message: `Failed to send notification: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

export async function notifyProposerAccepted(
  params: NotifyProposalAcceptedParams
): Promise<{ success: boolean; message: string }> {
  try {
    const { orderId, proposerId, proposedPrice, orderNumber } = params;

    NotificationServiceLogger.logBuyerNotificationStart({
      orderId,
      buyerId: proposerId,
      ...(orderNumber !== undefined && { orderNumber }),
    });

    // Get user data from Firebase
    const userData = await FirebaseUserService.getUserById(proposerId);

    const botUsername = await BotService.getBotUsername();
    const orderLink = OrderService.createOrderDeepLink(botUsername, orderId);

    // Create context for translation
    const ctx = {
      userLanguage: userData.userLanguage || "en",
    };

    // Prepare message parameters
    const messageParams = {
      orderNumber: orderNumber || "N/A",
      proposedPrice: proposedPrice.toString(),
      orderLink,
    };

    const message = T(ctx, botMessages.proposalAccepted.id, messageParams);

    // Send notification to proposer
    await bot.telegram.sendMessage(userData.tg_id, message, {
      parse_mode: "Markdown",
      link_preview_options: { is_disabled: true },
    });

    NotificationServiceLogger.logBuyerNotificationSuccess({
      orderId,
      buyerId: proposerId,
      tgId: userData.tg_id,
    });

    return {
      success: true,
      message: "Proposal accepted notification sent successfully",
    };
  } catch (error) {
    NotificationServiceLogger.logBuyerNotificationError(error, {
      orderId: params.orderId,
      buyerId: params.proposerId,
    });

    return {
      success: false,
      message: `Failed to send notification: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}
