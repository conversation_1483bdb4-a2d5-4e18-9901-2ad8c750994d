import {
  AppDate,
  GiftEntity,
  GIFTS_COLLECTION_NAME,
  GiftStatus,
  OrderEntity,
  ORDERS_COLLECTION_NAME,
  OrderStatus,
  TxType,
} from "../../../miker<PERSON>nko/marketplace-shared";
import { log } from "../../../utils/logger";
import { getFirestore } from "../../firebase-admin";
import { getAppConfig } from "../shared/app-config-service";
import {
  getUserBalance,
  spendFundsWithHistory,
} from "../shared/balance-service";
import { updateGiftOwnership } from "../shared/gift-service";
import { getUserById, resolveUserId } from "../shared/user-lookup";
import { validateGiftId, validateTelegramId } from "../shared/validation";

export interface WithdrawGiftParams {
  giftId: string;
  userTgId: string;
}

export interface WithdrawGiftResult {
  success: boolean;
  message?: string;
  error?: string;
  ownedGiftId?: string;
}

export async function withdrawGiftForBot(
  params: WithdrawGiftParams
): Promise<WithdrawGiftResult> {
  const { giftId, userTgId } = params;

  try {
    validateGiftId(giftId);
    validateTelegramId(userTgId);

    const db = getFirestore();

    log.info("Processing gift withdrawal", {
      operation: "withdraw_gift_for_bot",
      giftId,
      userTgId,
    });

    const giftDoc = await db
      .collection(GIFTS_COLLECTION_NAME)
      .doc(giftId)
      .get();

    if (!giftDoc.exists) {
      return {
        success: false,
        error: "Gift not found",
      };
    }

    const gift = { id: giftDoc.id, ...giftDoc.data() } as GiftEntity;

    // Validate gift belongs to user
    if (gift.owner_tg_id !== userTgId) {
      return {
        success: false,
        error: "Gift does not belong to this user",
      };
    }

    // Validate gift status
    if (gift.status !== GiftStatus.DEPOSITED) {
      return {
        success: false,
        error: "Gift is not available for withdrawal",
      };
    }

    // Get user data
    const userLookupResult = await resolveUserId({ tgId: userTgId });
    if (!userLookupResult.success || !userLookupResult.userId) {
      return {
        success: false,
        error: "User not found",
      };
    }

    const user = await getUserById(userLookupResult.userId);
    if (!user) {
      return {
        success: false,
        error: "User not found",
      };
    }

    // Check if gift is linked to any orders
    const ordersQuery = await db
      .collection(ORDERS_COLLECTION_NAME)
      .where("giftId", "==", giftId)
      .get();

    if (ordersQuery.empty) {
      // Case 3: Gift not linked to any order
      return await handleWithdrawalWithFee(
        giftDoc,
        user.id,
        "unlinked gift",
        gift.owned_gift_id
      );
    }

    const orders = ordersQuery.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as OrderEntity[];

    // First, check for any exclusionary conditions (active orders that prevent withdrawal)
    const hasActiveOrder = orders.some(
      (order) =>
        [
          OrderStatus.ACTIVE,
          OrderStatus.PAID,
          OrderStatus.CREATED,
          OrderStatus.GIFT_SENT_TO_RELAYER,
        ].includes(order.status) && order.sellerId === user.id
    );

    if (hasActiveOrder) {
      return {
        success: false,
        error:
          "Gift is currently linked to an active order and cannot be withdrawn",
      };
    }

    // Check for withdrawal-eligible conditions
    for (const order of orders) {
      // Case 1: Order with status "gift_sent_to_relayer" and user is buyer
      if (
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
        order.buyerId === user.id
      ) {
        return await handleBuyerWithdrawalFromActiveOrder(
          order,
          gift.owned_gift_id
        );
      }

      // Case 2: Order with status "cancelled" and user is seller
      if (
        order.status === OrderStatus.CANCELLED &&
        order.sellerId === user.id
      ) {
        return await handleSellerWithdrawalFromCancelledOrder(
          giftDoc,
          user.id,
          gift.owned_gift_id
        );
      }
    }

    return {
      success: false,
      error: "Gift is not eligible for withdrawal under current conditions",
    };
  } catch (error) {
    log.error("Failed to withdraw gift", error, {
      operation: "withdraw_gift_for_bot",
      giftId,
      userTgId,
    });
    throw error;
  }
}

async function handleBuyerWithdrawalFromActiveOrder(
  order: OrderEntity,
  ownedGiftId: string
): Promise<WithdrawGiftResult> {
  try {
    const db = getFirestore();

    // Update order status to fulfilled
    await db
      .collection(ORDERS_COLLECTION_NAME)
      .doc(order.id!)
      .update({
        status: OrderStatus.FULFILLED,
        updatedAt: new Date() as AppDate,
      });

    // Transfer gift ownership to buyer (mark as withdrawn)
    if (order.giftId && order.buyerId) {
      const buyer = await getUserById(order.buyerId);
      if (buyer?.tg_id) {
        await updateGiftOwnership(
          order.giftId,
          buyer.tg_id,
          GiftStatus.WITHDRAWN
        );
      }
    }

    log.info("Buyer withdrawal from active order completed", {
      operation: "handle_buyer_withdrawal_from_active_order",
      orderId: order.id,
      ownedGiftId,
    });

    return {
      success: true,
      message: "Gift withdrawn successfully (purchase completed)",
      ownedGiftId,
    };
  } catch (error) {
    log.error("Error in handleBuyerWithdrawalFromActiveOrder", error, {
      operation: "handle_buyer_withdrawal_from_active_order",
      orderId: order.id,
    });
    return {
      success: false,
      error: "Failed to process buyer withdrawal",
    };
  }
}

async function handleSellerWithdrawalFromCancelledOrder(
  giftDoc: FirebaseFirestore.DocumentSnapshot,
  userId: string,
  ownedGiftId: string
): Promise<WithdrawGiftResult> {
  return await handleWithdrawalWithFee(
    giftDoc,
    userId,
    "cancelled order",
    ownedGiftId
  );
}

async function handleWithdrawalWithFee(
  giftDoc: FirebaseFirestore.DocumentSnapshot,
  userId: string,
  withdrawalType: string,
  ownedGiftId: string
): Promise<WithdrawGiftResult> {
  try {
    const config = await getAppConfig();
    const withdrawalFee = config.withdrawal_fee || 0;

    // Check if user has enough balance for fee
    const userBalance = await getUserBalance(userId);
    const availableBalance = userBalance.sum - userBalance.locked;

    if (availableBalance < withdrawalFee) {
      return {
        success: false,
        error: "Insufficient balance to cover withdrawal fee",
      };
    }

    const db = getFirestore();
    const batch = db.batch();

    // Deduct withdrawal fee from user balance
    if (withdrawalFee > 0) {
      await spendFundsWithHistory({
        userId: userId,
        amount: withdrawalFee,
        txType: TxType.CANCELATION_FEE,
        description: `Gift withdrawal fee - ${withdrawalType}`,
        descriptionIntlKey: "transaction.withdrawal_fee",
        descriptionIntlParams: { type: withdrawalType },
      });
    }

    // Update gift status to withdrawn
    batch.update(giftDoc.ref, {
      status: GiftStatus.WITHDRAWN,
      updatedAt: new Date() as AppDate,
    });

    await batch.commit();

    log.info("Withdrawal with fee completed", {
      operation: "handle_withdrawal_with_fee",
      userId,
      withdrawalType,
      withdrawalFee,
      ownedGiftId,
    });

    return {
      success: true,
      message: `Gift withdrawn successfully (${withdrawalType})`,
      ownedGiftId,
    };
  } catch (error) {
    log.error("Error in handleWithdrawalWithFee", error, {
      operation: "handle_withdrawal_with_fee",
      userId,
      withdrawalType,
    });
    return {
      success: false,
      error: "Failed to process withdrawal with fee",
    };
  }
}
