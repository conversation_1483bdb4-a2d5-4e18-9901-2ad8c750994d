'use client';

import { Button } from '@telegram-apps/telegram-ui';

import { PriceLabel } from '@/components/shared/price-label';

interface PriceRowProps {
  label: string;
  amount: number;
  className?: string;
  tonLogoClassName?: string;
}

export function PriceRow({
  label,
  amount,
  className = '',
  tonLogoClassName = '',
}: PriceRowProps) {
  return (
    <div className={`flex items-center justify-between text-xs ${className}`}>
      <span>{label}</span>
      <PriceLabel
        amount={amount}
        size={24}
        tonLogoClassName={tonLogoClassName}
        clickable={false}
      />
    </div>
  );
}

interface PriceButtonProps {
  amount: number;
  className?: string;
  tonLogoClassName?: string;
}

export function PriceButton({
  amount,
  className = '',
  tonLogoClassName = '',
}: PriceButtonProps) {
  return (
    <Button
      className={`w-full [&>h6]:flex [&>h6]:items-center [&>h6]:justify-center [&>h6]:gap-1 ${className}`}
    >
      <PriceLabel
        amount={amount}
        size={24}
        tonLogoClassName={`-ml-1 ${tonLogoClassName}`}
        clickable={false}
      />
    </Button>
  );
}
