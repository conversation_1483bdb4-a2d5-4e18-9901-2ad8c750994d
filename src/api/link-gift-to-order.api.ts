import { httpsCallable } from 'firebase/functions';

import { AppCloudFunctions } from '@/core.constants';
import { firebaseFunctions } from '@/root-context';

interface LinkGiftToOrderRequest {
  giftId: string;
  orderId: string;
}

interface LinkGiftToOrderResponse {
  success: boolean;
  message: string;
}

export async function linkGiftToOrder(
  giftId: string,
  orderId: string,
): Promise<LinkGiftToOrderResponse> {
  try {
    const linkGiftToOrderFunction = httpsCallable<
      LinkGiftToOrderRequest,
      LinkGiftToOrderResponse
    >(firebaseFunctions, AppCloudFunctions.linkGiftToOrder);

    const result = await linkGiftToOrderFunction({
      giftId,
      orderId,
    });

    return result.data;
  } catch (error) {
    console.error('Error linking gift to order:', error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
