import { log } from "../../utils/logger";
import { LogOperations } from "../../constants/bot-commands";

export const SendGiftToPaidOrderFlowLogger = {
  logSendGiftToPaidOrderStarted(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.info("Send gift to paid order flow started", {
      operation: LogOperations.SEND_GIFT_TO_PAID_ORDER,
      ...params,
    });
  },

  logOrderNotFound(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.warn("Order not found for pending order ID", {
      operation: LogOperations.SEND_GIFT_TO_PAID_ORDER,
      ...params,
    });
  },

  logOrderNotPaidStatus(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    currentStatus: string;
  }) {
    log.warn("Order is not in PAID status for gift sending", {
      operation: LogOperations.SEND_GIFT_TO_PAID_ORDER,
      ...params,
    });
  },

  logUserNotSeller(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    sellerId?: string;
  }) {
    log.warn("User is not the seller of the order", {
      operation: LogOperations.SEND_GIFT_TO_PAID_ORDER,
      ...params,
    });
  },

  logGiftValidationFailed(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    collectionId: string;
    uniqueGift: any;
  }) {
    log.warn("Gift validation failed for paid order", {
      operation: LogOperations.SEND_GIFT_TO_PAID_ORDER,
      ...params,
    });
  },

  logGiftValidationError(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    error: unknown;
  }) {
    log.error(
      "Gift validation failed with exception for paid order",
      params.error,
      {
        operation: LogOperations.SEND_GIFT_TO_PAID_ORDER,
        chat_id: params.chat_id,
        userId: params.userId,
        pendingOrderId: params.pendingOrderId,
      }
    );
  },

  logBuyerNotificationSent(params: {
    orderId: string;
    buyerTgId: string;
    orderNumber: number;
  }) {
    log.info("Buyer notification sent successfully for paid order", {
      operation: LogOperations.SEND_GIFT_TO_PAID_ORDER,
      ...params,
    });
  },

  logBuyerNotificationError(params: {
    orderId: string;
    buyerTgId?: string;
    error: unknown;
  }) {
    log.error(
      "Failed to send buyer notification for paid order",
      params.error,
      {
        operation: LogOperations.SEND_GIFT_TO_PAID_ORDER,
        orderId: params.orderId,
        buyerTgId: params.buyerTgId,
      }
    );
  },

  logGiftSentToRelayerSuccess(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    collectionId: string;
  }) {
    log.info("Gift sent to relayer successfully for paid order", {
      operation: LogOperations.SEND_GIFT_TO_PAID_ORDER,
      ...params,
    });
  },
};
