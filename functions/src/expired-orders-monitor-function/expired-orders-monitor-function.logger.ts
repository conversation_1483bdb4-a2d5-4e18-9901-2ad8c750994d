import { createServiceLogger } from "../logger/base-logger";
import { LogOperations } from "../constants";

// Create service logger using functional composition
const expiredOrdersMonitorLogger = createServiceLogger(
  "expired-orders-monitor-function"
);

export const ExpiredOrdersMonitorLogger = {
  logProcessingStarted() {
    expiredOrdersMonitorLogger.logInfo(
      "Starting expired orders processing",
      LogOperations.PROCESS_EXPIRED,
      {}
    );
  },

  logNoOrdersFound() {
    expiredOrdersMonitorLogger.logInfo(
      "No expired orders found",
      LogOperations.PROCESS_EXPIRED,
      {}
    );
  },

  logOrdersFound({ count }: { count: number }) {
    expiredOrdersMonitorLogger.logInfo(
      `Found ${count} expired orders to process`,
      LogOperations.PROCESS_EXPIRED,
      { count }
    );
  },

  logOrderSkipped({ orderId, reason }: { orderId: string; reason: string }) {
    expiredOrdersMonitorLogger.logInfo(
      `Skipped order ${orderId}: ${reason}`,
      LogOperations.PROCESS_EXPIRED,
      { orderId, reason }
    );
  },

  logOrderProcessed({
    orderId,
    status,
    message,
  }: {
    orderId: string;
    status: string;
    message: string;
  }) {
    expiredOrdersMonitorLogger.logInfo(
      `Processed order ${orderId}: ${message}`,
      LogOperations.PROCESS_EXPIRED,
      { orderId, status, message }
    );
  },

  logOrderProcessError({
    error,
    orderId,
  }: {
    error: unknown;
    orderId: string;
  }) {
    expiredOrdersMonitorLogger.logError(
      `Error processing order ${orderId}`,
      error,
      LogOperations.PROCESS_EXPIRED,
      { orderId }
    );
  },

  logProcessingCompleted(): void {
    expiredOrdersMonitorLogger.logInfo(
      "Expired orders processing completed",
      LogOperations.PROCESS_EXPIRED,
      {}
    );
  },

  logProcessingError({ error }: { error: unknown }) {
    expiredOrdersMonitorLogger.logError(
      "Error in expired orders processing",
      error,
      LogOperations.PROCESS_EXPIRED,
      {}
    );
  },

  logMonitorTriggered({
    status,
    timestamp,
  }: {
    status: string;
    timestamp: string;
  }) {
    expiredOrdersMonitorLogger.logInfo(
      "Expired orders monitor triggered",
      LogOperations.MONITOR,
      { status, timestamp }
    );
  },

  logMonitorCompleted() {
    expiredOrdersMonitorLogger.logInfo(
      "Expired orders monitor completed",
      LogOperations.MONITOR,
      {}
    );
  },

  logMonitorFailed({ error, status }: { error: unknown; status: string }) {
    expiredOrdersMonitorLogger.logError(
      "Expired orders monitor failed",
      error,
      LogOperations.MONITOR,
      { status }
    );
  },
};
