output "service_url" {
  description = "The URL of the deployed Cloud Run service"
  value       = module.cloud_run.service_url
}

# output "redis_host" {
#   description = "Redis instance host"
#   value       = module.redis.redis_host
#   sensitive   = true
# }

output "artifact_registry_url" {
  description = "Artifact Registry repository URL"
  value       = module.artifact_registry.repository_url
}

output "github_actions_sa_key" {
  description = "GitHub Actions service account key"
  value       = module.iam.github_actions_sa_key
  sensitive   = true
}

output "gcp_project_id" {
  description = "Your project ID"
  value       = var.project_id
}
