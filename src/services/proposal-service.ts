import { proposalsTableMessages } from '@/components/order-details/proposals/intl/proposals-table.messages';
import type {
  OrderEntity,
  ProposalEntity,
  UserEntity,
} from '@/mikerudenko/marketplace-shared';
import { ProposalStatus } from '@/mikerudenko/marketplace-shared';

export const PROPOSAL_STATUS_MESSAGE_MAP = {
  [ProposalStatus.ACTIVE]: proposalsTableMessages.statusActive,
  [ProposalStatus.CANCELLED]: proposalsTableMessages.statusCancelled,
  [ProposalStatus.ACCEPTED]: proposalsTableMessages.statusAccepted,
} as const;

export const getProposalStatusMessageKey = (status: ProposalStatus) => {
  return PROPOSAL_STATUS_MESSAGE_MAP[status] || status;
};

export const PROPOSAL_STATUS_COLORS: Record<ProposalStatus, string> = {
  [ProposalStatus.ACTIVE]: 'text-green-400',
  [ProposalStatus.CANCELLED]: 'text-red-400',
  [ProposalStatus.ACCEPTED]: 'text-blue-400',
};

export function getProposalStatusColor(status: ProposalStatus): string {
  return PROPOSAL_STATUS_COLORS[status] || 'text-gray-400';
}

export function isUserSeller(
  order: OrderEntity,
  currentUser: UserEntity | null,
): boolean {
  return order.sellerId === currentUser?.id;
}

export function isUserProposer(
  proposal: ProposalEntity,
  currentUser: UserEntity | null,
): boolean {
  return proposal.proposer_id === currentUser?.id;
}
