import { onSchedule } from "firebase-functions/v2/scheduler";
import { commonFunctionsConfig } from "../constants";
import { processExpiredOrders } from "./expired-orders-monitor-function.service";
import { ExpiredOrdersMonitorLogger } from "./expired-orders-monitor-function.logger";

export { processExpiredOrders };

export const expiredOrdersMonitor = onSchedule(
  {
    schedule: "0 0 * * *", // Run daily at midnight UTC
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      ExpiredOrdersMonitorLogger.logMonitorTriggered({
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await processExpiredOrders();
      ExpiredOrdersMonitorLogger.logMonitorCompleted();
    } catch (error) {
      ExpiredOrdersMonitorLogger.logMonitorFailed({
        error,
        status: "monitor_failed",
      });
    }
  }
);
