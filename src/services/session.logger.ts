import { log } from "../utils/logger";
import { LogOperations } from "../constants/bot-commands";

export const SessionServiceLogger = {
  logSetSessionError(params: { error: unknown; userId: string }) {
    log.error(`Failed to set session for user ${params.userId}`, params.error, {
      operation: LogOperations.SET_USER_SESSION,
      userId: params.userId,
    });
  },

  logGetSessionError(params: { error: unknown; userId: string }) {
    log.error(`Failed to get session for user ${params.userId}`, params.error, {
      operation: LogOperations.GET_USER_SESSION,
      userId: params.userId,
    });
  },

  logClearSessionError(params: { error: unknown; userId: string }) {
    log.error(
      `Failed to clear session for user ${params.userId}`,
      params.error,
      {
        operation: LogOperations.CLEAR_USER_SESSION,
        userId: params.userId,
      }
    );
  },

  logUpdateSessionError(params: { error: unknown; userId: string }) {
    log.error(
      `Failed to update session for user ${params.userId}`,
      params.error,
      {
        operation: LogOperations.UPDATE_USER_SESSION,
        userId: params.userId,
      }
    );
  },

  logClearSessionPropertyError(params: {
    error: unknown;
    userId: string;
    property: string;
  }) {
    log.error(
      `Failed to clear session property ${params.property} for user ${params.userId}`,
      params.error,
      {
        operation: LogOperations.CLEAR_SESSION_PROPERTY,
        userId: params.userId,
        property: params.property,
      }
    );
  },
};
