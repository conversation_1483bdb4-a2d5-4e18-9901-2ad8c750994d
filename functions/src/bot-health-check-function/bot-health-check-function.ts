import { onSchedule } from "firebase-functions/v2/scheduler";
import { commonFunctionsConfig } from "../constants";
import { performBotHealthCheck } from "./bot-health-check-function.service";
import {
  logMonitorTriggered,
  logMonitorCompleted,
  logMonitorFailed,
} from "./bot-health-check-function.logger";

export const checkBotHealth = performBotHealthCheck;

export const botHealthCheck = onSchedule(
  {
    schedule: "*/15 * * * *", // Every 15 minutes
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      logMonitorTriggered({
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await performBotHealthCheck();
      logMonitorCompleted();
    } catch (error) {
      logMonitorFailed({
        error,
        status: "monitor_failed",
      });
    }
  }
);
