import { defineMessages } from "@formatjs/intl";

export const buttonsMessages = defineMessages({
  backToOrders: {
    id: "buttons.backToOrders",
    defaultMessage: "🔙 Back to Orders",
  },
  cancel: {
    id: "buttons.cancel",
    defaultMessage: "❌ Cancel",
  },
  contactSupport: {
    id: "buttons.contactSupport",
    defaultMessage: "📞 Contact Support",
  },
  depositGift: {
    id: "buttons.depositGift",
    defaultMessage: "📦 Deposit Gift",
  },
  myGifts: {
    id: "buttons.myGifts",
    defaultMessage: "🎁 My Gifts",
  },
  openMarketplace: {
    id: "buttons.openMarketplace",
    defaultMessage: "🌐 Open Marketplace",
  },
  viewAllOrders: {
    id: "buttons.viewAllOrders",
    defaultMessage: "📋 View All Orders",
  },
  viewMyOrders: {
    id: "buttons.viewMyOrders",
    defaultMessage: "👤 View My Orders",
  },
  orderHelpButton: {
    id: "buttons.orderHelpButton",
    defaultMessage: "📋 Order Help",
  },
  readyToSendGift: {
    id: "buttons.readyToSendGift",
    defaultMessage: "🎁 I'm ready to send gift",
  },
  buyOrders: {
    id: "buttons.buyOrders",
    defaultMessage: "🛒 Buy Orders",
  },
  sellOrders: {
    id: "buttons.sellOrders",
    defaultMessage: "💰 Sell Orders",
  },
  openMarketplaceButton: {
    id: "buttons.openMarketplaceButton",
    defaultMessage: "🌐 Open Marketplace",
  },
  withdrawGift: {
    id: "buttons.withdrawGift",
    defaultMessage: "Withdraw {giftName} {orderInfo}",
  },
});
