import { createServiceLogger } from "../logger/base-logger";
import { LogOperations } from "../constants";

// Create service logger using functional composition
const userProfileLogger = createServiceLogger("user-profile-function");

export function logReferrerSet(userId: string, referrerId: string) {
  userProfileLogger.logInfo(
    `Setting referrer_id for user ${userId}: ${referrerId}`,
    LogOperations.USER_PROFILE,
    {
      userId,
      referrer_id: referrerId,
      action: "set_referrer",
    }
  );
}

export function logReferrerPointsUpdated(data: {
  referrerId: string;
  referralCount: number;
  previousPoints: number;
  newPoints: number;
}) {
  userProfileLogger.logInfo(
    "Updated referrer points",
    LogOperations.REFERRER_POINTS,
    {
      referrer_id: data.referrerId,
      referrer_user_id: data.referrerId,
      referral_count: data.referralCount,
      previous_points: data.previousPoints,
      new_points: data.newPoints,
    }
  );
}

export function logReferrerNotFound(data: { referrerId: string }) {
  userProfileLogger.logWarn(
    `Referrer with user ID ${data.referrerId} not found`,
    LogOperations.REFERRER_POINTS,
    {
      referrer_id: data.referrerId,
      action: "referrer_not_found",
    }
  );
}

export function logReferrerPointsError(data: {
  error: unknown;
  referrerId: string;
  userId: string;
}) {
  userProfileLogger.logError(
    "Error updating referrer points",
    data.error,
    LogOperations.REFERRER_POINTS,
    {
      referrer_id: data.referrerId,
      userId: data.userId,
    }
  );
}

export function logReferrerUpdateSkipped(data: {
  userId: string;
  existingReferrerId: string;
  attemptedReferrerId: string;
}) {
  userProfileLogger.logInfo(
    `User ${data.userId} already has referrer_id: ${data.existingReferrerId}, not updating`,
    LogOperations.USER_PROFILE,
    {
      userId: data.userId,
      existing_referrer_id: data.existingReferrerId,
      attempted_referrer_id: data.attemptedReferrerId,
      action: "skip_referrer_update",
    }
  );
}

export function logProfileUpdated(data: {
  userId: string;
  updatedFields: string[];
  preparedData: any;
}) {
  userProfileLogger.logInfo(
    `User profile updated for ${data.userId}`,
    LogOperations.USER_PROFILE,
    {
      userId: data.userId,
      updatedFields: data.updatedFields,
      preparedData: data.preparedData,
    }
  );
}

export function logProfileUpdateError(data: {
  error: unknown;
  userId: string;
  requestData: any;
}) {
  userProfileLogger.logError(
    "Error in changeUserData function",
    data.error,
    LogOperations.USER_PROFILE,
    {
      userId: data.userId,
      requestData: data.requestData,
    }
  );
}
