import { onSchedule } from "firebase-functions/v2/scheduler";
import { commonFunctionsConfig } from "../constants";
import { checkLimitedCollections } from "./limited-collections-monitor-function.service";
import { LimitedCollectionsMonitorLogger } from "./limited-collections-monitor-function.logger";

export const limitedCollectionsMonitor = onSchedule(
  {
    schedule: "0 */6 * * *", // Every 6 hours
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      LimitedCollectionsMonitorLogger.logMonitorTriggered({
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await checkLimitedCollections();
      LimitedCollectionsMonitorLogger.logMonitorCompleted();
    } catch (error) {
      LimitedCollectionsMonitorLogger.logMonitorFailed({
        error,
        status: "monitor_failed",
      });
    }
  }
);
