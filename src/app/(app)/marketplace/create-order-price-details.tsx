import { PriceLabel } from '@/components/shared/price-label';
import { TonLogo } from '@/components/TonLogo';

interface CreateOrderPriceDetailsProps {
  price: number;
  lockPercentage: number;
  lockAmount: number;
  availableBalance: number;
  hasSufficientBalance: boolean;
  areLockAndCollateralHidden?: boolean;
}

export function CreateOrderPriceDetails({
  price,
  lockPercentage,
  lockAmount,
  availableBalance,
  hasSufficientBalance,
  areLockAndCollateralHidden = false,
}: CreateOrderPriceDetailsProps) {
  return (
    <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-[#708499]">Item price:</span>
          <PriceLabel
            amount={price}
            size={24}
            className="text-[#6ab2f2] font-semibold"
            clickable={false}
          />
        </div>
        {!areLockAndCollateralHidden && (
          <div className="flex justify-between items-center">
            <span className="text-[#708499]">
              Your Collateral Locked Percentage (
              {(lockPercentage * 100).toFixed(0)}
              %):
            </span>
            <PriceLabel
              amount={lockAmount}
              size={24}
              className="text-[#6ab2f2] font-semibold"
              clickable={false}
            />
          </div>
        )}
        <div className="flex justify-between items-center">
          <span className="text-[#708499]">Available balance:</span>
          <PriceLabel
            amount={availableBalance}
            size={24}
            className="text-[#6ab2f2] font-semibold"
            clickable={false}
          />
        </div>
        <div className="flex justify-between items-center font-medium border-t border-[#3a4a5c]/30 pt-3">
          <span className="text-[#f5f5f5]">Remaining after lock:</span>
          <div className="flex items-center gap-1">
            <span
              className={`font-semibold ${
                hasSufficientBalance ? 'text-[#6ab2f2]' : 'text-[#ec3942]'
              }`}
            >
              {(availableBalance - lockAmount).toFixed(2)}
            </span>
            <TonLogo size={24} />
          </div>
        </div>
      </div>
    </div>
  );
}
