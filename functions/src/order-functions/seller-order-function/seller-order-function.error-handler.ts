import { HttpsError } from "firebase-functions/v2/https";
import { GENERIC_ERRORS } from "../../error-messages";

export class SellerOrderFunctionErrorHandler {
  static throwCreateOrderError(message?: string): never {
    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage: message ?? "Server error while creating order.",
      })
    );
  }

  static throwPurchaseError(message?: string): never {
    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage: message ?? "Server error while making purchase.",
      })
    );
  }
}
