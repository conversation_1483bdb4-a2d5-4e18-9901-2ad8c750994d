import * as admin from "firebase-admin";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { TelegramClient, Api } from "telegram";
import { StringSession } from "telegram/sessions/index.js";
import {
  AppDate,
  COLLECTION_NAME,
  CollectionEntity,
  CollectionStatus,
} from "../miker<PERSON>nko/marketplace-shared";
import {
  getTelegramBotToken,
  getTelegramApiId,
  getTelegramApiHash,
} from "../config";
import { addDeadlineToOrders as addDeadlineToOrdersService } from "../services/deadline-service/deadline-service";
import { commonFunctionsConfig } from "../constants";
import { LimitedCollectionsMonitorLogger } from "./limited-collections-monitor-function.logger";

const db = admin.firestore();

interface LimitedGift {
  id: string;
  limited: boolean;
  upgradeStars: number | null;
}

async function fetchLimitedCollections() {
  const botToken = getTelegramBotToken();
  const apiId = getTelegramApiId();
  const apiHash = getTelegramApiHash();

  const stringSession = new StringSession("");
  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });

  try {
    await client.start({
      botAuthToken: botToken,
    });

    const result = await client.invoke(
      new Api.payments.GetStarGifts({ hash: 0 })
    );

    // Handle the result properly based on its type
    if (!("gifts" in result)) {
      LimitedCollectionsMonitorLogger.logNoGiftsFound();
      return [];
    }

    const limitedGifts = (result as any).gifts
      .filter((gift: any) => gift.limited === true)
      .map((gift: any) => ({
        id: gift.id.toString(),
        limited: gift.limited,
        upgradeStars: gift.upgradeStars ? gift.upgradeStars.toString() : null,
      }));

    LimitedCollectionsMonitorLogger.logLimitedGiftsFound({
      count: limitedGifts.length,
    });
    return limitedGifts;
  } catch (error) {
    LimitedCollectionsMonitorLogger.logTelegramApiError({ error });
    throw error;
  } finally {
    await client.disconnect();
  }
}

async function updateCollectionToMarket(collectionId: string) {
  const collectionRef = db.collection(COLLECTION_NAME).doc(collectionId);
  const collectionDoc = await collectionRef.get();

  if (!collectionDoc.exists) {
    LimitedCollectionsMonitorLogger.logCollectionNotFound({ collectionId });
    return;
  }

  const collection = collectionDoc.data() as CollectionEntity;

  // Only set launchedAt if it's not already set
  const updateData: Partial<CollectionEntity> = {
    status: CollectionStatus.MARKET,
  };

  if (!collection.launchedAt) {
    updateData.launchedAt = admin.firestore.Timestamp.now() as AppDate;
    LimitedCollectionsMonitorLogger.logLaunchedAtSet({ collectionId });
  } else {
    LimitedCollectionsMonitorLogger.logLaunchedAtExists({ collectionId });
    return;
  }

  await collectionRef.update(updateData);

  LimitedCollectionsMonitorLogger.logCollectionUpdatedToMarket({
    collectionId,
  });
}

async function addDeadlineToOrders(collectionId: string) {
  await addDeadlineToOrdersService(db, collectionId);
}

async function createNewCollection(gift: LimitedGift) {
  const newCollection: CollectionEntity = {
    id: gift.id,
    name: "Limited Gift",
    description: "Limited collection",
    status: CollectionStatus.PREMARKET,
    floorPrice: 0.1, // Default floor price in TON
    active: true, // New collections are active by default
  };

  await db.collection(COLLECTION_NAME).doc(gift.id).set(newCollection);
  LimitedCollectionsMonitorLogger.logNewCollectionCreated({
    collectionId: gift.id,
  });
}

async function processUpgradeableCollection(gift: LimitedGift) {
  try {
    const collectionRef = db.collection(COLLECTION_NAME).doc(gift.id);
    const collectionDoc = await collectionRef.get();

    if (!collectionDoc.exists) {
      LimitedCollectionsMonitorLogger.logCollectionCreationNotFound({
        collectionId: gift.id,
      });
      await createNewCollection(gift);

      // After creating, we need to get the collection data to continue processing
      const newCollectionDoc = await collectionRef.get();
      if (!newCollectionDoc.exists) {
        LimitedCollectionsMonitorLogger.logCollectionCreationFailed({
          collectionId: gift.id,
        });
        return;
      }
    }

    // Get the collection data (either existing or newly created)
    const updatedCollectionDoc = await collectionRef.get();
    const collection = updatedCollectionDoc.data() as CollectionEntity;

    // Skip if not PREMARKET status
    if (collection.status !== CollectionStatus.PREMARKET) {
      LimitedCollectionsMonitorLogger.logCollectionStatusNotPremarket({
        collectionId: gift.id,
        status: collection.status,
      });
      return;
    }

    LimitedCollectionsMonitorLogger.logProcessingCollectionWithUpgradeStars({
      collectionId: gift.id,
      upgradeStars: gift.upgradeStars?.toString() || "",
    });

    // Update collection status to MARKET and set launchedAt
    await updateCollectionToMarket(gift.id);

    // Add deadlines to orders without them
    await addDeadlineToOrders(gift.id);

    LimitedCollectionsMonitorLogger.logCollectionProcessed({
      collectionId: gift.id,
      status: "completed",
    });
  } catch (error) {
    LimitedCollectionsMonitorLogger.logMonitorError({ error });
    throw error;
  }
}

async function ensureCollectionExists(gift: LimitedGift) {
  try {
    const collectionRef = db.collection(COLLECTION_NAME).doc(gift.id);
    const collectionDoc = await collectionRef.get();

    if (!collectionDoc.exists) {
      LimitedCollectionsMonitorLogger.logCollectionCreationNotFound({
        collectionId: gift.id,
      });
      await createNewCollection(gift);
    } else {
      LimitedCollectionsMonitorLogger.logCollectionAlreadyExists({
        collectionId: gift.id,
      });
    }
  } catch (error) {
    LimitedCollectionsMonitorLogger.logEnsureCollectionError({
      error,
      collectionId: gift.id,
    });
    throw error;
  }
}

export async function checkLimitedCollections() {
  try {
    LimitedCollectionsMonitorLogger.logMonitorStarted({ status: "started" });

    const limitedGifts = await fetchLimitedCollections();

    if (limitedGifts.length === 0) {
      LimitedCollectionsMonitorLogger.logNoLimitedCollections();
      return;
    }

    LimitedCollectionsMonitorLogger.logCollectionsFound({
      count: limitedGifts.length,
    });

    // First, ensure all limited collections exist in Firestore
    for (const gift of limitedGifts) {
      try {
        await ensureCollectionExists(gift);
      } catch (error) {
        LimitedCollectionsMonitorLogger.logEnsureCollectionFailed({
          error,
          collectionId: gift.id,
        });
        // Continue processing other collections even if one fails
      }
    }

    // Then, process upgradeable collections (those with upgradeStars)
    const upgradeableGifts = limitedGifts.filter(
      // @ts-expect-error: upgradeStars is not null
      (gift) => gift.upgradeStars !== null
    );

    if (upgradeableGifts.length === 0) {
      LimitedCollectionsMonitorLogger.logNoUpgradeableCollections();
      return;
    }

    LimitedCollectionsMonitorLogger.logUpgradeableCollectionsFound({
      count: upgradeableGifts.length,
    });

    for (const gift of upgradeableGifts) {
      try {
        await processUpgradeableCollection(gift);
      } catch (error) {
        LimitedCollectionsMonitorLogger.logProcessCollectionFailed({
          error,
          collectionId: gift.id,
        });
      }
    }

    LimitedCollectionsMonitorLogger.logMonitorCompleted();
  } catch (error) {
    LimitedCollectionsMonitorLogger.logMonitorError({ error });
    throw error;
  }
}

export const limitedCollectionsMonitor = onSchedule(
  {
    schedule: "0 1 * * *", // Run daily at 1 AM UTC
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      LimitedCollectionsMonitorLogger.logMonitorTriggered({
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await checkLimitedCollections();
      LimitedCollectionsMonitorLogger.logMonitorCompleted();
    } catch (error) {
      LimitedCollectionsMonitorLogger.logMonitorFailed({
        error,
        status: "monitor_failed",
      });
    }
  }
);
