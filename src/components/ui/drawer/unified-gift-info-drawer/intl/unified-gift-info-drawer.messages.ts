import { defineMessages } from 'react-intl';

export const unifiedGiftInfoDrawerMessages = defineMessages({
  sendGiftToRelayer: {
    id: 'unifiedGiftInfoDrawer.sendGiftToRelayer',
    defaultMessage: 'Send Gift to Relayer',
  },
  claimYourGift: {
    id: 'unifiedGiftInfoDrawer.claimYourGift',
    defaultMessage: 'Claim Your Gift',
  },
  activateYourOrder: {
    id: 'unifiedGiftInfoDrawer.activateYourOrder',
    defaultMessage: 'Activate Your Order',
  },
  getCancelledGift: {
    id: 'unifiedGiftInfoDrawer.getCancelledGift',
    defaultMessage: 'Get Cancelled Gift',
  },
  sendGiftSteps: {
    id: 'unifiedGiftInfoDrawer.sendGiftSteps',
    defaultMessage:
      "Send the gift directly to the relayer, then return to the app and attach the gift to your order using the 'Attach Gift' button",
  },
  claimGiftSteps: {
    id: 'unifiedGiftInfoDrawer.claimGiftSteps',
    defaultMessage:
      "Go to the bot, select 'My Gifts', and select the specific gift you want to withdraw. You will see the corresponding order for that gift. Then go to the primary relayer and write message 'Get a gift'",
  },
  activateOrderSteps: {
    id: 'unifiedGiftInfoDrawer.activateOrderSteps',
    defaultMessage:
      'Send the gift directly to the relayer, return to the app, and attach the corresponding gift to your order',
  },
  getCancelledGiftSteps: {
    id: 'unifiedGiftInfoDrawer.getCancelledGiftSteps',
    defaultMessage: 'Follow these steps to retrieve your cancelled gift',
  },
  // Instructions
  goToBot: {
    id: 'unifiedGiftInfoDrawer.goToBot',
    defaultMessage: 'Go to {botLink}',
  },
  pressMySellOrders: {
    id: 'unifiedGiftInfoDrawer.pressMySellOrders',
    defaultMessage: 'Press on "My Sell Orders" button',
  },
  pressMySellOrdersPaid: {
    id: 'unifiedGiftInfoDrawer.pressMySellOrdersPaid',
    defaultMessage:
      'Press on "My Sell Orders" button and select from "Paid Orders" group',
  },
  pressMySellOrdersWaitingActivation: {
    id: 'unifiedGiftInfoDrawer.pressMySellOrdersWaitingActivation',
    defaultMessage:
      'Press on "My Sell Orders" button and select from "Waiting for Activation" group',
  },
  pressMySellOrdersCancelled: {
    id: 'unifiedGiftInfoDrawer.pressMySellOrdersCancelled',
    defaultMessage: 'Click on "My Gifts" button',
  },
  pressMuyBuyOrders: {
    id: 'unifiedGiftInfoDrawer.pressMuyBuyOrders',
    defaultMessage: 'Press on "My Buy Orders" button',
  },
  selectOrderToSend: {
    id: 'unifiedGiftInfoDrawer.selectOrderToSend',
    defaultMessage: 'Select your order you want to send',
  },
  selectOrderToGet: {
    id: 'unifiedGiftInfoDrawer.selectOrderToGet',
    defaultMessage: 'Select your order you want to get',
  },
  selectOrderToActivate: {
    id: 'unifiedGiftInfoDrawer.selectOrderToActivate',
    defaultMessage:
      'Select the gift you want to withdraw related to this cancelled order',
  },
  confirmAndSendToRelayer: {
    id: 'unifiedGiftInfoDrawer.confirmAndSendToRelayer',
    defaultMessage: 'Confirm action, and send this gift to {relayerLink}',
  },
  sendGiftToRelayerToActivate: {
    id: 'unifiedGiftInfoDrawer.sendGiftToRelayerToActivate',
    defaultMessage: 'Send your gift to {relayerLink} to activate this order',
  },
  confirmAndGoToRelayer: {
    id: 'unifiedGiftInfoDrawer.confirmAndGoToRelayer',
    defaultMessage: 'Confirm action, and go to {relayerLink} to get your gift',
  },
  goToRelayerToRetrieve: {
    id: 'unifiedGiftInfoDrawer.goToRelayerToRetrieve',
    defaultMessage: 'Go to "Primary lawyer" and write "Get my gift"',
  },
  // Deposit gift instructions
  depositGiftToBot: {
    id: 'unifiedGiftInfoDrawer.depositGiftToBot',
    defaultMessage:
      'Deposit your gift to the bot using the "Deposit a Gift" button',
  },
  instructions: {
    id: 'unifiedGiftInfoDrawer.instructions',
    defaultMessage: 'Instructions:',
  },
  close: {
    id: 'unifiedGiftInfoDrawer.close',
    defaultMessage: 'Close',
  },
  openBot: {
    id: 'unifiedGiftInfoDrawer.openBot',
    defaultMessage: 'Open Bot',
  },
});
