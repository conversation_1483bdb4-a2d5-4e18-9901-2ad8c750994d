import { defineMessages } from 'react-intl';

export const withdrawGiftDrawerMessages = defineMessages({
  withdrawGift: {
    id: 'withdrawGiftDrawer.withdrawGift',
    defaultMessage: 'Withdraw Gift',
  },
  withdrawInstructions: {
    id: 'withdrawGiftDrawer.withdrawInstructions',
    defaultMessage:
      "1. Go to the bot, click on 'My Gifts', and select the gift you want to withdraw 2. Then, go to the primary relayer and write a message 'Get a gift'",
  },
  instructionStep1: {
    id: 'withdrawGiftDrawer.instructionStep1',
    defaultMessage:
      'Go to {botLink}, click on "My Gifts", and select the gift you want to withdraw',
  },
  instructionStep2: {
    id: 'withdrawGiftDrawer.instructionStep2',
    defaultMessage:
      'Then, go to {relayerLink} and write a message "Get a gift"',
  },

  openBot: {
    id: 'withdrawGiftDrawer.openBot',
    defaultMessage: 'Open Bot',
  },
  close: {
    id: 'withdrawGiftDrawer.close',
    defaultMessage: 'Close',
  },
});
