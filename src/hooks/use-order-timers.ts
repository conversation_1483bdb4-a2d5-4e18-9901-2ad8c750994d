import { useEffect, useState } from 'react';

import {
  type CollectionEntity,
  firebaseTimestampToDate,
  type OrderEntity,
} from '@/mikerudenko/marketplace-shared';
import {
  calculateDeadlineInfo,
  calculateFreezeStatus,
  formatTimeLeft,
  shouldShowDeadlineTimer,
  type TimerState,
} from '@/services/order-service';

interface UseOrderTimersProps {
  order: OrderEntity | null;
  collection: CollectionEntity | null;
}

export function useOrderTimers({
  order,
  collection,
}: UseOrderTimersProps): TimerState {
  const [timeLeft, setTimeLeft] = useState<string>('');
  const [isFreezed, setIsFreezed] = useState<boolean>(false);

  useEffect(() => {
    if (!order) return;

    const updateTimers = () => {
      // Update freeze status
      const freezeStatus = calculateFreezeStatus(collection);
      setIsFreezed(freezeStatus);

      // Update deadline countdown
      if (shouldShowDeadlineTimer(order)) {
        const deadlineInfo = calculateDeadlineInfo(
          firebaseTimestampToDate(order.deadline!),
        );
        const formattedTime = formatTimeLeft(deadlineInfo);
        setTimeLeft(formattedTime);
      } else {
        setTimeLeft('');
      }
    };

    updateTimers();
    const interval = setInterval(updateTimers, 1000);

    return () => clearInterval(interval);
  }, [order, collection]);

  return { timeLeft, isFreezed };
}
