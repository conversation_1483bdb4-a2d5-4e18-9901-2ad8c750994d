'use client';

import { RefreshCw, Star } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import { getRelayerStarsData, type RelayerStarsData } from '@/api/order-api';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { STARS_PER_GIFT_SEND } from '@/core.constants';
import { useToast } from '@/hooks/use-toast';

export const StarsCountDisplay = () => {
  const { toast } = useToast();
  const [starsData, setStarsData] = useState<RelayerStarsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const loadStarsData = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await getRelayerStarsData();
      setStarsData(data);
    } catch (error) {
      console.error('Error loading relayer stars data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load relayer stars data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await loadStarsData();
      toast({
        title: 'Success',
        description: 'Relayer stars data refreshed successfully',
      });
    } catch (error) {
      console.error('Error refreshing relayer stars data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    loadStarsData();
  }, []);

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Star className="h-4 w-4" />
          Relayer Stars Requirement
        </CardTitle>
        <CardDescription className="text-sm">
          Stars needed for all orders requiring relayer action
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Order Type Breakdown */}
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-muted-foreground">
              Orders Requiring Stars:
            </h4>

            <div className="grid grid-cols-1 gap-2">
              <div className="flex items-center justify-between py-1">
                <span className="text-sm text-muted-foreground">
                  Paid Orders:
                </span>
                <span className="text-sm font-medium">
                  {isLoading
                    ? 'Loading...'
                    : (starsData?.paidOrders?.toLocaleString() ?? '0')}
                </span>
              </div>

              <div className="flex items-center justify-between py-1">
                <span className="text-sm text-muted-foreground">
                  Active Orders (with gift):
                </span>
                <span className="text-sm font-medium">
                  {isLoading
                    ? 'Loading...'
                    : (starsData?.activeOrdersWithGift?.toLocaleString() ??
                      '0')}
                </span>
              </div>

              <div className="flex items-center justify-between py-1">
                <span className="text-sm text-muted-foreground">
                  Cancelled Orders (with gift):
                </span>
                <span className="text-sm font-medium">
                  {isLoading
                    ? 'Loading...'
                    : (starsData?.cancelledOrdersWithGift?.toLocaleString() ??
                      '0')}
                </span>
              </div>

              <div className="flex items-center justify-between py-1">
                <span className="text-sm text-muted-foreground">
                  Gift Sent to Relayer:
                </span>
                <span className="text-sm font-medium">
                  {isLoading
                    ? 'Loading...'
                    : (starsData?.giftSentToRelayerOrders?.toLocaleString() ??
                      '0')}
                </span>
              </div>
            </div>
          </div>

          <div className="border-t pt-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Total Orders:</span>
              <span className="text-lg font-bold">
                {isLoading
                  ? 'Loading...'
                  : (starsData?.totalOrdersNeedingStars?.toLocaleString() ??
                    '0')}
              </span>
            </div>

            <div className="flex items-center justify-between mt-2">
              <span className="text-sm font-medium">Stars per Order:</span>
              <span className="text-lg font-bold">{STARS_PER_GIFT_SEND}</span>
            </div>

            <div className="flex items-center justify-between mt-3 pt-3 border-t">
              <span className="text-sm font-medium">Total Stars Needed:</span>
              <span className="text-xl font-bold text-primary">
                {isLoading
                  ? 'Loading...'
                  : (starsData?.totalStarsNeeded?.toLocaleString() ?? '0')}
              </span>
            </div>
          </div>

          <div className="bg-muted p-3 rounded-md">
            <p className="text-sm text-muted-foreground">
              Total stars needed on relayer:{' '}
              <span className="font-semibold text-foreground">
                {starsData?.totalOrdersNeedingStars?.toLocaleString() ?? '0'}{' '}
                orders × {STARS_PER_GIFT_SEND} stars ={' '}
                {starsData?.totalStarsNeeded?.toLocaleString() ?? '0'} stars
              </span>
            </p>
          </div>

          <Button
            onClick={handleRefresh}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
            className="w-full"
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`}
            />
            {isRefreshing ? 'Refreshing...' : 'Refresh Data'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
