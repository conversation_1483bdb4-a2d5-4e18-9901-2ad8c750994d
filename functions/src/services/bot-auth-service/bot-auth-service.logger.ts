import { createServiceLogger } from "../../logger/logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const botAuthServiceLogger = createServiceLogger("bot-auth-service");

export function logBotAuthSuccess({ operation }: { operation: string }) {
  botAuthServiceLogger.logInfo(
    "Bot authentication successful",
    LogOperations.BOT_OPERATION,
    {
      operation,
    }
  );
}

export function logBotAuthError({
  error,
  operation,
}: {
  error: unknown;
  operation: string;
}) {
  botAuthServiceLogger.logError(
    `Error in bot authentication: ${operation}`,
    error,
    LogOperations.BOT_OPERATION,
    { operation }
  );
}
