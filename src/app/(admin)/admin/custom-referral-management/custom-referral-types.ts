import { z } from 'zod';

export const customReferralSchema = z.object({
  telegramHandle: z.string().min(1, 'Telegram handle is required'),
  referralFee: z.number().min(0, 'Referral fee must be non-negative'),
});

export type CustomReferralFormData = z.infer<typeof customReferralSchema>;

export interface UserWithCustomReferral {
  id: string;
  displayName?: string | null;
  email?: string | null;
  tg_id?: string;
  telegram_handle?: string;
  referral_fee: number;
}
