export const CACHE_CONFIG = { duration: 24 * 60 * 60 * 1000 }; // 1 day

export enum CachePatterns {
  ORDERS_FOR_BUYERS = 'getOrdersForBuyers',
  ORDERS_FOR_SELLERS = 'getOrdersForSellers',
  SECONDARY_MARKET_ORDERS = 'getSecondaryMarketOrders',
  USER_ORDERS = 'getUserOrders',
  ACTIVITY_ORDERS = 'getActivityOrders',
  COLLECTIONS = 'collections',
  APP_CONFIG = 'app_config',
}

export enum AppCloudFunctions {
  withdrawRevenue = 'withdrawRevenue',
  setSecondaryMarketPrice = 'setSecondaryMarketPrice',
  makeSecondaryMarketPurchase = 'makeSecondaryMarketPurchase',
  cancelUserOrder = 'cancelUserOrder',
  makePurchaseAsSeller = 'makePurchaseAsSeller',
  makePurchaseAsBuyer = 'makePurchaseAsBuyer',
  changeUserData = 'changeUserData',
  signInWithTelegram = 'signInWithTelegram',
  recalculateOrderDeadlines = 'recalculateOrderDeadlines',
  clearOrderDeadlines = 'clearOrderDeadlines',
  fulfillOrderAndCreateResellOrder = 'fulfillOrderAndCreateResellOrder',
  linkGiftToOrder = 'linkGiftToOrder',
  createSellOrderFromGift = 'createSellOrderFromGift',
  createOrderAsSeller = 'createOrderAsSeller',
  createOrderAsBuyer = 'createOrderAsBuyer',
  PROPOSE_ORDER_PRICE = 'proposeOrderPrice',
  CANCEL_PROPOSAL = 'cancelProposal',
  ACCEPT_PROPOSAL = 'acceptProposal',
}

export const STARS_PER_GIFT_SEND = 25;

export const COLLECTION_CACHE_CONFIG = { duration: 24 * 60 * 60 * 1000 }; // 1 day

export enum AppRoutes {
  MARKETPLACE = '/',
  ACTIVITY = '/activity',
  ORDERS = '/orders',
  GIFTS = '/gifts',
  PROFILE = '/profile',
  ADMIN = '/admin',
  AUTH = '/auth',
}

export const TELEGRAM_BOT_URL = `https://t.me/${process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME}`;

export const PREM_RELAYER_USERNAME = 'premrelayer';
export const PREM_RELAYER_URL = `https://t.me/${PREM_RELAYER_USERNAME}`;

export const FREEZE_PERIOD_DAYS = 21;
export const FREEZE_PERIOD_MS = FREEZE_PERIOD_DAYS * 24 * 60 * 60 * 1000;

export const ADMIN_DEFAULT_NAME = 'Prem Distributor';

export const CDN_BASE_URL = 'https://cdn.changes.tg/gifts/originals';

export const COINGECKO_API_URL =
  'https://api.coingecko.com/api/v3/simple/price?ids=the-open-network&vs_currencies=usd';

export const DATE_FORMAT_TRANSACTION = 'MMM d, yyyy HH:mm';
export const DATE_FORMAT_SHORT = 'MMM d, yyyy';

export const APP_CURRENCY = 'TON';

export enum AppLocale {
  en = 'en',
  ru = 'ru',
  ua = 'ua',
}

export enum LocalStorageKeys {
  IS_ANIMATED_COLLECTION = 'is_animated_collection',
  LOCALE = 'locale',
  WELCOME_MODAL_SHOWN = 'welcomeModalShown',
}

export const FIREBASE_REGION = 'europe-central2';

export const SHOW_ACTIVITY_TAB = false;

export const BASE_TG_UI_STYLES = {
  '--tgui--bg_color': 'var(--tg-theme-bg-color)',
  '--tgui--secondary_bg_color': 'var(--tg-theme-secondary-bg-color)',
  '--tgui--text_color': 'var(--tg-theme-text-color)',
  '--tgui--hint_color': 'var(--tg-theme-hint-color)',
  '--tgui--link_color': 'var(--tg-theme-link-color)',
  '--tgui--button_color': 'var(--tg-theme-button-color)',
  '--tgui--button_text_color': 'var(--tg-theme-button-text-color)',
  '--tgui--secondary_hint_color': 'var(--tg-theme-subtitle-text-color)',
  '--tgui--outline': 'var(--tg-theme-section-separator-color)',
  '--tgui--accent_text_color': 'var(--tg-theme-accent-text-color)',
  '--tgui--section_bg_color': 'var(--tg-theme-section-bg-color)',
  '--tgui--header_bg_color': 'var(--tg-theme-header-bg-color)',
  '--tgui--destructive_text_color': 'var(--tg-theme-destructive-text-color)',
  '--ring': 'var(--tg-theme-accent-text-color)',
};
