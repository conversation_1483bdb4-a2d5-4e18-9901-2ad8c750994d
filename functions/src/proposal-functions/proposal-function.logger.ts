import { createServiceLogger } from "../logger/base-logger";
import { LogOperations } from "../constants";

// Create service logger using functional composition
const proposalLogger = createServiceLogger("proposal");

export const ProposalLogger = {
  logProposalCreated({
    orderId,
    proposerId,
    proposedPrice,
    originalPrice,
    collateralAmount,
  }: {
    orderId: string;
    proposerId: string;
    proposedPrice: number;
    originalPrice: number;
    collateralAmount: number;
  }) {
    proposalLogger.logInfo(
      "Price proposal created successfully",
      LogOperations.PROPOSAL_CREATE,
      {
        orderId,
        proposerId,
        proposedPrice,
        originalPrice,
        collateralAmount,
      }
    );
  },

  logProposalCancelled({
    orderId,
    proposerId,
    proposalId,
    refundAmount,
  }: {
    orderId: string;
    proposerId: string;
    proposalId: string;
    refundAmount: number;
  }) {
    proposalLogger.logInfo(
      "Price proposal cancelled successfully",
      LogOperations.PROPOSAL_CANCEL,
      {
        orderId,
        proposerId,
        proposalId,
        refundAmount,
      }
    );
  },

  logProposalAccepted({
    orderId,
    proposalId,
    sellerId,
    proposerId,
    proposedPrice,
    originalPrice,
    newOrderPrice,
  }: {
    orderId: string;
    proposalId: string;
    sellerId: string;
    proposerId: string;
    proposedPrice: number;
    originalPrice: number;
    newOrderPrice: number;
  }) {
    proposalLogger.logInfo(
      "Price proposal accepted successfully",
      LogOperations.PROPOSAL_ACCEPT,
      {
        orderId,
        proposalId,
        sellerId,
        proposerId,
        proposedPrice,
        originalPrice,
        newOrderPrice,
      }
    );
  },

  logProposalValidationFailed({
    orderId,
    proposerId,
    proposedPrice,
    reason,
  }: {
    orderId: string;
    proposerId: string;
    proposedPrice: number;
    reason: string;
  }) {
    proposalLogger.logWarn(
      "Proposal validation failed",
      LogOperations.PROPOSAL_VALIDATION,
      {
        orderId,
        proposerId,
        proposedPrice,
        reason,
      }
    );
  },

  logProposalError({
    error,
    operation,
    orderId,
    proposerId,
    proposalId,
  }: {
    error: unknown;
    operation: string;
    orderId?: string;
    proposerId?: string;
    proposalId?: string;
  }) {
    proposalLogger.logError(
      `Error in proposal operation: ${operation}`,
      error,
      LogOperations.PROPOSAL_OPERATION,
      {
        orderId,
        proposerId,
        proposalId,
      }
    );
  },

  logCollateralRefund({
    orderId,
    proposerId,
    refundAmount,
    reason,
  }: {
    orderId: string;
    proposerId: string;
    refundAmount: number;
    reason: string;
  }) {
    proposalLogger.logInfo(
      "Proposal collateral refunded",
      LogOperations.PROPOSAL_COLLATERAL_REFUND,
      {
        orderId,
        proposerId,
        refundAmount,
        reason,
      }
    );
  },

  logSellerCollateralAdjustment({
    orderId,
    sellerId,
    oldCollateral,
    newCollateral,
    refundAmount,
  }: {
    orderId: string;
    sellerId: string;
    oldCollateral: number;
    newCollateral: number;
    refundAmount: number;
  }) {
    proposalLogger.logInfo(
      "Seller collateral adjusted after proposal acceptance",
      LogOperations.PROPOSAL_SELLER_COLLATERAL_ADJUSTMENT,
      {
        orderId,
        sellerId,
        oldCollateral,
        newCollateral,
        refundAmount,
      }
    );
  },
};
