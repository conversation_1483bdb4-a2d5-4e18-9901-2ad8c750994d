import { defineMessages } from "@formatjs/intl";

export const callbacksMessages = defineMessages({
  backToMenu: {
    id: "callbacks.backToMenu",
    defaultMessage: "🏠 Back to Main Menu",
  },
  buyOrdersTitle: {
    id: "callbacks.buyOrdersTitle",
    defaultMessage: "🛒 Your Buy Orders ({count} total)",
  },
  cancelledOrdersWithGifts: {
    id: "callbacks.cancelledOrdersWithGifts",
    defaultMessage:
      "\n\n🔴 Cancelled Orders with Gifts ({count})\nThese orders were cancelled but have gifts that can be refunded.",
  },
  chooseOrderType: {
    id: "callbacks.chooseOrderType",
    defaultMessage:
      "📋 Choose Order Type\n\nSelect the type of orders you want to view:",
  },
  callbackContactSupport: {
    id: "callbacks.contactSupport",
    defaultMessage: `📞 Contact Support\n\nFor any questions or issues, please reach out to {PREM_SUPPORT_OFFICIAL}`,
  },
  fetchBuyOrdersError: {
    id: "callbacks.fetchBuyOrdersError",
    defaultMessage: "❌ Error fetching buy orders. Please try again.",
  },
  fetchGroupOrdersError: {
    id: "callbacks.fetchGroupOrdersError",
    defaultMessage: "❌ Error fetching orders. Please try again.",
  },
  fetchSellOrdersError: {
    id: "callbacks.fetchSellOrdersError",
    defaultMessage: "❌ Error fetching sell orders. Please try again.",
  },
  callbackGenericError: {
    id: "callbacks.genericError",
    defaultMessage: "❌ An error occurred. Please try again.",
  },
  giftReadyForDelivery: {
    id: "callbacks.giftReadyForDelivery",
    defaultMessage:
      "🎁 Your gift is ready for delivery! Click the button below to receive it.",
  },
  giftReceivedSuccess: {
    id: "callbacks.giftReceivedSuccess",
    defaultMessage: "🎁 Gift received successfully! Enjoy your new item.",
  },
  group1OrderReady: {
    id: "callbacks.group1OrderReady",
    defaultMessage:
      "🟠 This order has been paid and is waiting for you to send the gift to the relayer.",
  },
  group1Title: {
    id: "callbacks.group1Title",
    defaultMessage: "🟠 Paid Orders Awaiting Gift ({count} total)",
  },
  group2OrderReady: {
    id: "callbacks.group2OrderReady",
    defaultMessage:
      "🔵 This order needs a gift to be activated. Please deposit a gift first.",
  },
  group2Title: {
    id: "callbacks.group2Title",
    defaultMessage: "🔵 Created Orders Needing Activation ({count} total)",
  },
  group3OrderReady: {
    id: "callbacks.group3OrderReady",
    defaultMessage:
      "🔴 This order was cancelled but has a gift that can be refunded.",
  },
  group3Title: {
    id: "callbacks.group3Title",
    defaultMessage: "🔴 Cancelled Orders with Gifts ({count} total)",
  },
  noActionableSellOrders: {
    id: "callbacks.noActionableSellOrders",
    defaultMessage: "📭 No actionable sell orders found.",
  },
  noBuyOrders: {
    id: "callbacks.noBuyOrders",
    defaultMessage:
      "📭 No Buy Orders\n\nYou don't have any buy orders at the moment.",
  },
  noGroup1Orders: {
    id: "callbacks.noGroup1Orders",
    defaultMessage: "📭 No paid orders awaiting gifts found.",
  },
  noGroup2Orders: {
    id: "callbacks.noGroup2Orders",
    defaultMessage: "📭 No created orders needing activation found.",
  },
  noGroup3Orders: {
    id: "callbacks.noGroup3Orders",
    defaultMessage: "📭 No cancelled orders with gifts found.",
  },
  noSellOrders: {
    id: "callbacks.noSellOrders",
    defaultMessage:
      "📭 No Sell Orders\n\nYou don't have any sell orders at the moment.",
  },
  openingMarketplace: {
    id: "callbacks.openingMarketplace",
    defaultMessage: "🌐 Opening Marketplace...",
  },
  orderHelp: {
    id: "callbacks.orderHelp",
    defaultMessage:
      "❓ Order Help\n\nIf you need assistance with your orders, please contact our support team.",
  },
  ordersNeedActivation: {
    id: "callbacks.ordersNeedActivation",
    defaultMessage:
      "\n\n🔵 Orders Need Activation ({count})\nThese orders need a gift to be activated.",
  },
  sellOrdersTitle: {
    id: "callbacks.sellOrdersTitle",
    defaultMessage: "💰 Your Sell Orders",
  },
  showOrderOptionsError: {
    id: "callbacks.showOrderOptionsError",
    defaultMessage: "❌ Error showing order options. Please try again.",
  },
  orderStatusActive: {
    id: "callbacks.orderStatus.active",
    defaultMessage: "✅ This order is active and ready for completion.",
  },
  orderStatusPaid: {
    id: "callbacks.orderStatus.paid",
    defaultMessage:
      "💰 This order has been paid and is waiting for gift delivery.",
  },
  orderStatusCreated: {
    id: "callbacks.orderStatus.created",
    defaultMessage: "📝 This order has been created and needs activation.",
  },
  orderStatusCancelled: {
    id: "callbacks.orderStatus.cancelled",
    defaultMessage: "❌ This order has been cancelled.",
  },
  orderStatusGiftSentToRelayer: {
    id: "callbacks.orderStatus.gift_sent_to_relayer",
    defaultMessage: "🎁 Gift has been sent to relayer for delivery.",
  },
});
