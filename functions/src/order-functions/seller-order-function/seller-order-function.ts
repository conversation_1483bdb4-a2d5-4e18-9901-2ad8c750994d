import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig, LogOperations } from "../../constants";
import {
  validateCreateOrderRequest,
  createSellerOrder,
  validatePurchaseRequest,
  processSellerPurchase,
} from "./seller-order-function.service";
import { SellerOrderLogger } from "./seller-order-function.logger";
import { SellerOrderFunctionErrorHandler } from "./seller-order-function.error-handler";

export const createOrderAsSeller = onCall<{
  sellerId: string;
  collectionId: string;
  price: number;
  giftId?: string;
}>(commonFunctionsConfig, async (request) => {
  const { sellerId, collectionId, price, giftId } = request.data;

  try {
    await validateCreateOrderRequest(request, {
      sellerId,
      collectionId,
      price,
      giftId,
    });

    const result = await createSellerOrder({
      sellerId,
      collectionId,
      price,
      giftId,
    });

    return result;
  } catch (error) {
    SellerOrderLogger.logCreateOrderError({
      error,
      sellerId,
      operation: LogOperations.CREATE_ORDER_AS_SELLER,
      requestData: { collectionId, price, giftId },
    });

    SellerOrderFunctionErrorHandler.throwCreateOrderError(
      (error as any).message
    );
  }
});

export const makePurchaseAsSeller = onCall<{
  sellerId: string;
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  const { sellerId, orderId } = request.data;

  try {
    await validatePurchaseRequest(request, {
      sellerId,
      orderId,
    });

    const result = await processSellerPurchase({
      sellerId,
      orderId,
    });

    return result;
  } catch (error) {
    SellerOrderLogger.logPurchaseError({
      error,
      operation: LogOperations.PURCHASE_AS_SELLER,
      requestData: request.data,
      userId: request.auth?.uid,
    });

    SellerOrderFunctionErrorHandler.throwPurchaseError((error as any).message);
  }
});
