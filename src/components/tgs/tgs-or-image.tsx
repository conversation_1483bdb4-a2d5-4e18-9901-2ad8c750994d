'use client';

import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useEffect, useState } from 'react';

import { TgsSkeleton } from '@/components/tgs/tgs-skeleton';
import { getImagePathWithFallback, isUserPidor } from '@/lib/utils';

const TgsViewer = dynamic(
  () => import('./tgs-viewer').then((mod) => ({ default: mod.TgsViewer })),
  {
    ssr: false,
    loading: () => (
      <TgsSkeleton
        className="w-full h-full"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
        }}
      />
    ),
  },
);

enum TgsFallbackState {
  CDN_TGS = 'cdn_tgs',
  LOCAL_TGS = 'local_tgs',
  CDN_PNG = 'cdn_png',
  LOCAL_PNG = 'local_png',
  FAILED = 'failed',
}

enum ImageFallbackState {
  LOCAL_PNG = 'local_png',
  CDN_PNG = 'cdn_png',
  FAILED = 'failed',
}

function useImageFallbackChain(collectionId: string) {
  const [currentState, setCurrentState] = useState<ImageFallbackState>(
    ImageFallbackState.LOCAL_PNG,
  );
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [imageLoaded, setImageLoaded] = useState<boolean>(false);

  useEffect(() => {
    if (!collectionId) {
      setCurrentSrc('');
      setCurrentState(ImageFallbackState.LOCAL_PNG);
      setIsLoading(false);
      setImageLoaded(false);
      return;
    }

    setCurrentState(ImageFallbackState.LOCAL_PNG);
    setIsLoading(true);
    setImageLoaded(false);

    const { fallback: localPng } = getImagePathWithFallback(
      collectionId,
      'png',
    );
    setCurrentSrc(localPng);
  }, [collectionId]);

  const handleError = () => {
    setImageLoaded(false);
    switch (currentState) {
      case ImageFallbackState.LOCAL_PNG: {
        setCurrentState(ImageFallbackState.CDN_PNG);
        setIsLoading(true);
        const { primary: cdnPng } = getImagePathWithFallback(
          collectionId,
          'png',
        );
        setCurrentSrc(cdnPng);
        break;
      }
      case ImageFallbackState.CDN_PNG:
        setCurrentState(ImageFallbackState.FAILED);
        setCurrentSrc('');
        setIsLoading(false);
        break;
      default:
        break;
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    setImageLoaded(true);
  };

  return {
    src: currentSrc,
    isFailed: currentState === ImageFallbackState.FAILED,
    isLoading,
    imageLoaded,
    onError: handleError,
    onLoad: handleLoad,
    currentState,
  };
}

function useTgsFallbackChain(collectionId: string) {
  const [currentState, setCurrentState] = useState<TgsFallbackState>(
    TgsFallbackState.CDN_TGS,
  );
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [isImage, setIsImage] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [contentLoaded, setContentLoaded] = useState<boolean>(false);

  useEffect(() => {
    if (!collectionId) {
      setCurrentSrc('');
      setCurrentState(TgsFallbackState.CDN_TGS);
      setIsImage(false);
      setIsLoading(false);
      setContentLoaded(false);
      return;
    }

    setCurrentState(TgsFallbackState.CDN_TGS);
    setIsImage(false);
    setIsLoading(true);
    setContentLoaded(false);

    const { primary: cdnTgs } = getImagePathWithFallback(collectionId, 'tgs');
    setCurrentSrc(cdnTgs);
  }, [collectionId]);

  const handleError = () => {
    setContentLoaded(false);
    switch (currentState) {
      case TgsFallbackState.CDN_TGS: {
        setCurrentState(TgsFallbackState.LOCAL_TGS);
        setIsImage(false);
        setIsLoading(true);
        const { fallback: localTgs } = getImagePathWithFallback(
          collectionId,
          'tgs',
        );
        setCurrentSrc(localTgs);
        break;
      }

      case TgsFallbackState.LOCAL_TGS: {
        setCurrentState(TgsFallbackState.CDN_PNG);
        setIsImage(true);
        setIsLoading(true);
        const { primary: cdnPng } = getImagePathWithFallback(
          collectionId,
          'png',
        );
        setCurrentSrc(cdnPng);
        break;
      }

      case TgsFallbackState.CDN_PNG: {
        setCurrentState(TgsFallbackState.LOCAL_PNG);
        setIsImage(true);
        setIsLoading(true);
        const { fallback: localPng } = getImagePathWithFallback(
          collectionId,
          'png',
        );
        setCurrentSrc(localPng);
        break;
      }

      case TgsFallbackState.LOCAL_PNG:
        setCurrentState(TgsFallbackState.FAILED);
        setCurrentSrc('');
        setIsImage(false);
        setIsLoading(false);
        break;

      default:
        break;
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    setContentLoaded(true);
  };

  return {
    src: currentSrc,
    isImage,
    isFailed: currentState === TgsFallbackState.FAILED,
    isLoading,
    contentLoaded,
    onError: handleError,
    onLoad: handleLoad,
    currentState,
  };
}

interface TgsOrImageProps {
  isImage: boolean;
  collectionId: string;
  imageProps?: {
    alt: string;
    fill?: boolean;
    className?: string;
    loading?: 'lazy' | 'eager';
    sizes?: string;
    onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  };
  tgsProps?: {
    style?: React.CSSProperties;
    className?: string;
  };
}

export function TgsOrImage({
  isImage,
  collectionId,
  imageProps,
  tgsProps,
}: TgsOrImageProps) {
  const imageFallback = useImageFallbackChain(collectionId);
  const tgsFallback = useTgsFallbackChain(collectionId);

  if (!collectionId) {
    return null;
  }

  const shouldForceImage = isImage || isUserPidor();

  if (shouldForceImage && imageProps) {
    if (imageFallback.isFailed || !imageFallback.src) {
      return (
        <TgsSkeleton
          className={imageProps.className}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '200%',
            height: '200%',
          }}
        />
      );
    }

    return (
      <div className="relative w-full h-full">
        {/* Show skeleton while loading */}
        {!imageFallback.imageLoaded && (
          <TgsSkeleton
            className={imageProps.className}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '200%',
              height: '200%',
              zIndex: 10,
            }}
          />
        )}

        {/* Image that loads and becomes visible when ready */}
        <Image
          src={imageFallback.src}
          alt={imageProps.alt}
          className={imageProps.className}
          loading={imageProps.loading}
          sizes={
            imageProps.sizes ||
            (imageProps.fill
              ? '(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw'
              : undefined)
          }
          fill={imageProps.fill}
          width={imageProps.fill ? undefined : 200}
          height={imageProps.fill ? undefined : 200}
          style={{
            display: 'block',
            opacity: imageFallback.imageLoaded ? 1 : 0,
            transition: 'opacity 0.2s ease-in-out',
          }}
          onLoad={() => {
            imageFallback.onLoad();
          }}
          onError={(e) => {
            imageFallback.onError();
            imageProps.onError?.(e);
          }}
        />
      </div>
    );
  }

  if (!shouldForceImage && tgsProps) {
    if (tgsFallback.isFailed || !tgsFallback.src) {
      return (
        <TgsSkeleton className={tgsProps.className} style={tgsProps.style} />
      );
    }

    if (tgsFallback.isImage) {
      return (
        <>
          {/* Show skeleton while loading */}
          {tgsFallback.isLoading && (
            <TgsSkeleton
              className={tgsProps.className}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '200%',
                height: '200%',
                zIndex: 10,
              }}
            />
          )}
          <div className="relative w-full h-full">
            <Image
              src={tgsFallback.src}
              alt={imageProps?.alt || 'Collection image'}
              className={tgsProps.className}
              style={{
                ...tgsProps.style,
                opacity: tgsFallback.contentLoaded ? 1 : 0,
                transition: 'opacity 0.2s ease-in-out',
              }}
              width={200}
              height={200}
              onError={tgsFallback.onError}
              onLoad={tgsFallback.onLoad}
            />
          </div>
        </>
      );
    }

    return (
      <>
        {tgsFallback.isLoading && (
          <TgsSkeleton
            className={tgsProps.className}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '200%',
              height: '200%',
              zIndex: 10,
              ...tgsProps.style,
            }}
          />
        )}
        <div className="relative w-full h-full">
          {/* Show skeleton while loading TGS */}
          <TgsViewer
            tgsUrl={tgsFallback.src}
            style={{
              ...tgsProps.style,
              opacity: tgsFallback.contentLoaded ? 1 : 0,
              transition: 'opacity 0.2s ease-in-out',
            }}
            onError={tgsFallback.onError}
            onLoad={tgsFallback.onLoad}
            className={tgsProps.className}
          />
        </div>
      </>
    );
  }

  return null;
}
