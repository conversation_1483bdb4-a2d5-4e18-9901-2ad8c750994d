'use client';

import { TabsList } from '@telegram-apps/telegram-ui';
import { TabsItem } from '@telegram-apps/telegram-ui/dist/components/Navigation/TabsList/components/TabsItem/TabsItem';
import { useIntl } from 'react-intl';

import { SHOW_ACTIVITY_TAB } from '@/core.constants';

import { marketplaceTabsMessages } from './intl/marketplace-tabs.messages';
import type { TabType } from './use-marketplace-orders';

interface MarketplaceTabsProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

export const MarketplaceTabs = ({
  activeTab,
  onTabChange,
}: MarketplaceTabsProps) => {
  const { formatMessage: t } = useIntl();

  const tabs = [
    { id: 'buyers' as const, message: marketplaceTabsMessages.buy },
    { id: 'sellers' as const, message: marketplaceTabsMessages.sell },
    { id: 'activity' as const, message: marketplaceTabsMessages.activity },
  ].filter((tab) => tab.id !== 'activity' || SHOW_ACTIVITY_TAB);

  return (
    <TabsList
      className={`grid w-full ${SHOW_ACTIVITY_TAB ? 'grid-cols-3' : 'grid-cols-2'} gap-0!`}
    >
      {tabs.map((tab) => (
        <TabsItem
          key={tab.id}
          selected={activeTab === tab.id}
          onClick={() => onTabChange(tab.id)}
        >
          {t(tab.message)}
        </TabsItem>
      ))}
    </TabsList>
  );
};
