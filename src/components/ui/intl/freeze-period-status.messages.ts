import { defineMessages } from 'react-intl';

export const freezePeriodStatusMessages = defineMessages({
  freezePeriodNotStarted: {
    id: 'freezePeriodStatus.freezePeriodNotStarted',
    defaultMessage: "Freeze period hasn't started yet",
  },
  freezePeriodEnded: {
    id: 'freezePeriodStatus.freezePeriodEnded',
    defaultMessage: 'Freeze period has ended',
  },
  expired: {
    id: 'freezePeriodStatus.expired',
    defaultMessage: 'Expired',
  },
  timeRemaining: {
    id: 'freezePeriodStatus.timeRemaining',
    defaultMessage: '{days}d {hours}h {minutes}m {seconds}s remaining',
  },
});
