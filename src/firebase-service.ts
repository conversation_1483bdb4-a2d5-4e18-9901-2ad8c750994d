import { loadEnvironment } from "./config/env-loader";
import { OrderEntity, OrderGift } from "./mikerudenko/marketplace-shared";
import { log } from "./utils/logger";

// Import local Firebase functions
import {
  getUserGiftsAvailableForWithdrawalForBot,
  withdrawGiftForBot,
} from "./firebase/services/gift-functions";
import { depositGiftDirectlyForBot } from "./firebase/services/order-functions";

loadEnvironment();

// Legacy enum kept for compatibility - no longer used for local functions
export enum CloudFunctionsNames {
  sendGiftToRelayerByBot = "sendGiftToRelayerByBot", // Still uses cloud function
}

export interface GetUserOrdersResponse {
  success: boolean;
  orders: OrderEntity[]; // All orders (for backward compatibility)
  sellOrders: OrderEntity[]; // Orders where user is seller
  paidOrdersAwaitingGift: OrderEntity[]; // Paid orders waiting for gift
  createdOrdersNeedingActivation: OrderEntity[]; // Created orders for MARKET collections
  cancelledOrdersWithGifts: OrderEntity[]; // Cancelled orders with gifts for refund
  buyOrders: OrderEntity[]; // Orders where user is buyer
  count: number; // Total count
  sellOrdersCount: number; // Count of sell orders
  paidOrdersAwaitingGiftCount: number; // Count of paid orders awaiting gift
  createdOrdersNeedingActivationCount: number; // Count of created orders needing activation
  cancelledOrdersWithGiftsCount: number; // Count of cancelled orders with gifts
  buyOrdersCount: number; // Count of buy orders
  userId: string;
  message: string; // Response message
}

export interface CompletePurchaseResponse {
  success: boolean;
  message: string;
  netAmountToSeller: number;
  feeAmount: number;
  order: {
    id: string;
    number: number;
    status: string;
  };
}

export async function depositGiftDirectly(
  userTgId: string,
  gift: OrderGift,
  collectionId?: string
) {
  try {
    const params: any = {
      userTgId,
      gift,
    };

    if (collectionId) {
      params.collectionId = collectionId;
    }

    return await depositGiftDirectlyForBot(params);
  } catch (error) {
    log.error("Error depositing gift directly", error, {
      operation: "deposit_gift_directly",
      userTgId,
      gift,
      collectionId,
    });
    throw new Error("Failed to deposit gift directly");
  }
}

export async function getUserGiftsAvailableForWithdrawal(
  tgId: string
): Promise<any[]> {
  if (!tgId) {
    throw new Error("Telegram ID is required");
  }

  try {
    log.info(
      "Calling getUserGiftsAvailableForWithdrawalForBot local function",
      {
        operation: "get_user_gifts_available_for_withdrawal",
        tgId,
      }
    );

    const result = await getUserGiftsAvailableForWithdrawalForBot({
      tg_id: tgId,
    });

    log.info("Get user gifts available for withdrawal completed", {
      operation: "get_user_gifts_available_for_withdrawal",
      tgId,
      giftsCount: result?.length || 0,
    });

    return result || [];
  } catch (error) {
    log.error("Error getting user gifts available for withdrawal", error, {
      operation: "get_user_gifts_available_for_withdrawal",
      tgId,
    });

    throw new Error("Failed to get user gifts available for withdrawal");
  }
}

export async function withdrawGiftByBot(params: {
  giftId: string;
  userTgId: string;
}): Promise<{
  success: boolean;
  message?: string;
  error?: string;
  ownedGiftId?: string;
}> {
  const { giftId, userTgId } = params;

  try {
    log.info("Calling withdrawGiftForBot local function", {
      operation: "withdraw_gift_by_bot",
      giftId,
      userTgId,
    });

    const result = await withdrawGiftForBot({
      giftId,
      userTgId,
    });

    log.info("Withdraw gift completed", {
      operation: "withdraw_gift_by_bot",
      giftId,
      userTgId,
      success: result.success,
    });

    return result;
  } catch (error) {
    log.error("Error withdrawing gift", error, {
      operation: "withdraw_gift_by_bot",
      giftId,
      userTgId,
    });
    throw new Error("Failed to withdraw gift");
  }
}
