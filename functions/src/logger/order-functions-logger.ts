import { createServiceLogger } from "./logger";
import { LogOperations } from "../constants";

// Create service loggers using functional composition
const buyerOrderLogger = createServiceLogger("buyer-order");
const sellerOrderLogger = createServiceLogger("seller-order");
const generalOrderLogger = createServiceLogger("general-order");
const fulfillAndResellLogger = createServiceLogger("fulfill-and-resell");
const transactionHistoryLogger = createServiceLogger("transaction-history");
const feeServiceLogger = createServiceLogger("fee-service");

export const OrderFunctionsLogger = {
  // Buyer Order Logger Functions
  logBuyerCreateOrderError({
    error,
    requestData,
    userId,
  }: {
    error: unknown;
    requestData?: any;
    userId?: string;
  }) {
    buyerOrderLogger.logError(
      "Error creating order as buyer",
      error,
      LogOperations.CREATE_ORDER_AS_BUYER,
      {
        requestData,
        userId,
      }
    );
  },

  logBuyerPurchaseError({
    error,
    buyerId,
    orderId,
  }: {
    error: unknown;
    buyerId?: string;
    orderId?: string;
  }) {
    buyerOrderLogger.logError(
      "Error making purchase as buyer",
      error,
      LogOperations.BUYER_PURCHASE,
      {
        buyerId,
        orderId,
      }
    );
  },

  // Seller Order Logger Functions
  logSellerCreateOrderError({
    error,
    requestData,
    sellerId,
  }: {
    error: unknown;
    requestData?: any;
    sellerId?: string;
  }) {
    sellerOrderLogger.logError(
      "Error creating order as seller",
      error,
      LogOperations.CREATE_ORDER_AS_SELLER,
      {
        sellerId,
        collectionId: requestData?.collectionId,
        price: requestData?.price,
      }
    );
  },

  logSellerPurchaseError({
    error,
    requestData,
    userId,
  }: {
    error: unknown;
    requestData?: any;
    userId?: string;
  }) {
    sellerOrderLogger.logError(
      "Error making purchase as seller",
      error,
      LogOperations.PURCHASE_AS_SELLER,
      {
        requestData,
        userId,
      }
    );
  },

  // General Order Logger Functions
  logCancelOrderRequest({
    orderId,
    userId,
    environment,
  }: {
    orderId: string;
    userId: string;
    environment?: string;
  }) {
    generalOrderLogger.logDebug(
      "Cancel order request received",
      LogOperations.CANCEL_ORDER,
      {
        orderId,
        userId,
        environment,
      }
    );
  },

  logCancelOrderError({
    error,
    orderId,
    userId,
  }: {
    error: unknown;
    orderId?: string;
    userId?: string;
  }) {
    generalOrderLogger.logError(
      "Error cancelling order",
      error,
      LogOperations.CANCEL_USER_ORDER,
      {
        orderId,
        userId,
      }
    );
  },

  // Fulfill and Resell Logger Functions
  logOrderFulfilledAndResellCreated({
    originalOrderId,
    newOrderId,
    userId,
    price,
  }: {
    originalOrderId: string;
    newOrderId: string;
    userId: string;
    price: number;
  }) {
    fulfillAndResellLogger.logInfo(
      "Order fulfilled and resell order created",
      LogOperations.FULFILL_AND_RESELL,
      {
        originalOrderId,
        newOrderId,
        userId,
        price,
      }
    );
  },

  logFulfillAndResellError({
    error,
    requestData,
    userId,
  }: {
    error: unknown;
    requestData?: any;
    userId?: string;
  }) {
    fulfillAndResellLogger.logError(
      "Error fulfilling order and creating resell order",
      error,
      LogOperations.FULFILL_AND_RESELL,
      {
        requestData,
        userId,
      }
    );
  },

  // Transaction History Logger Functions
  logTransactionRecordCreated({
    userId,
    txType,
    originalAmount,
    signedAmount,
    orderId,
  }: {
    userId: string;
    txType: string;
    originalAmount: number;
    signedAmount: number;
    orderId?: string;
  }) {
    transactionHistoryLogger.logInfo(
      "Transaction history record created",
      LogOperations.CREATE_TRANSACTION_RECORD,
      {
        userId,
        txType,
        originalAmount,
        signedAmount,
        orderId,
      }
    );
  },

  // Fee Service Logger Functions
  logAppConfigNotFound() {
    feeServiceLogger.logWarn(
      "App config not found, using zero fees",
      LogOperations.APP_CONFIG_FETCH,
      {}
    );
  },
};
