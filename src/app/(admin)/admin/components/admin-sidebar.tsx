'use client';

import {
  ArrowLeft,
  BarChart3,
  FolderOpen,
  Settings,
  ShoppingCart,
  UserPlus,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { cn } from '@/lib/utils';

interface MenuItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const menuItems: MenuItem[] = [
  {
    href: '/admin',
    label: 'Dashboard',
    icon: BarChart3,
    description: 'Revenue & Statistics',
  },
  {
    href: '/admin/app-config',
    label: 'App Config',
    icon: Settings,
    description: 'Fees & Settings',
  },
  {
    href: '/admin/collections',
    label: 'Collections',
    icon: FolderOpen,
    description: 'Manage Collections',
  },
  {
    href: '/admin/orders',
    label: 'Orders',
    icon: ShoppingCart,
    description: 'Order Management',
  },
  {
    href: '/admin/users',
    label: 'Users',
    icon: Users,
    description: 'User Management',
  },
  {
    href: '/admin/referrals',
    label: 'Referrals',
    icon: UserPlus,
    description: 'Custom Referrals',
  },
  {
    href: '/',
    label: 'Back to Marketplace',
    icon: ArrowLeft,
    description: 'Back to Marketplace',
  },
];

export const AdminSidebar = () => {
  const pathname = usePathname();

  return (
    <div className="w-64 bg-card border-r border-border shadow-sm">
      <div className="p-6">
        <h1 className="text-xl font-bold text-foreground">Admin Panel</h1>
        <p className="text-sm text-muted-foreground mt-1">
          Marketplace Management
        </p>
      </div>

      <nav className="px-4 pb-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <li key={item.href}>
                <Link
                  href={item.href}
                  className={cn(
                    'flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                    isActive
                      ? 'bg-primary/10 text-primary border border-primary/20'
                      : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground',
                  )}
                >
                  <Icon className="h-5 w-5" />
                  <div className="flex-1">
                    <div>{item.label}</div>
                    <div className="text-xs text-muted-foreground mt-0.5">
                      {item.description}
                    </div>
                  </div>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
};
