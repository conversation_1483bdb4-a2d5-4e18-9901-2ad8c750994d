export { changeUserData } from "./user-profile-function";
export {
  logReferrerSet,
  logReferrerPointsUpdated,
  logReferrerNotFound,
  logReferrerPointsError,
  logReferrerUpdateSkipped,
  logProfileUpdated,
  logProfileUpdateError,
} from "./user-profile-function.logger";
export {
  getUserData,
  validateWalletAddress,
  prepareUpdateData,
  handleReferrerUpdate,
  updateUserProfile,
} from "./user-profile-function.service";
export {
  throwUserNotFound,
  throwWalletAlreadyUsed,
  throwUserProfileInternalError,
} from "./user-profile-function.error-handler";

export type {
  ChangeUserDataParams,
  ChangeUserDataResult,
} from "./user-profile-function.service";
