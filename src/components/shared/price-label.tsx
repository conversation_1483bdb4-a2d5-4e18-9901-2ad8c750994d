'use client';

import { useState } from 'react';

import { TonLogo } from '@/components/TonLogo';
import { useTonExchangeRate } from '@/contexts/ton-exchange-rate-context';
import { APP_CURRENCY } from '@/core.constants';

interface PriceLabelProps {
  amount: number;
  size?: number;
  className?: string;
  tonLogoClassName?: string;
  showUnit?: boolean;
  clickable?: boolean;
}

export function PriceLabel({
  amount,
  size = 24,
  className = '',
  tonLogoClassName = '',
  showUnit = false,
  clickable = true,
}: PriceLabelProps) {
  const [showUsd, setShowUsd] = useState(false);
  const { convertTonToUsd, isLoading } = useTonExchangeRate();

  const formattedTonAmount =
    typeof amount === 'number' ? amount.toFixed(2) : amount;
  const usdAmount = convertTonToUsd(Number(amount));
  const formattedUsdAmount = usdAmount.toFixed(2);

  const handleClick = () => {
    if (clickable && !isLoading) {
      setShowUsd(!showUsd);
    }
  };

  const containerClassName = `flex items-center gap-1 ${className} ${
    clickable && !isLoading
      ? 'cursor-pointer hover:opacity-80 transition-opacity'
      : ''
  }`;

  if (showUsd && !isLoading) {
    return (
      <div className={containerClassName} onClick={handleClick}>
        <span>${formattedUsdAmount}</span>
        {showUnit && <span className="text-sm text-[#708499]">USD</span>}
      </div>
    );
  }

  return (
    <div className={containerClassName} onClick={handleClick}>
      <span>{formattedTonAmount}</span>
      <TonLogo size={size} className={tonLogoClassName} />
      {showUnit && (
        <span className="text-sm text-[#708499]">{APP_CURRENCY}</span>
      )}
    </div>
  );
}
