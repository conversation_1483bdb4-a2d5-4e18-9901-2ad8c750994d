import { onSchedule } from "firebase-functions/v2/scheduler";
import { commonFunctionsConfig } from "../constants";
import { monitorTonTransactions } from "./ton-monitor-function.service";
import { TonMonitorLogger } from "./ton-monitor-function.logger";

export const tonMonitor = onSchedule(
  {
    schedule: "*/5 * * * *", // Every 5 minutes
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      TonMonitorLogger.logMonitorTriggered({
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await monitorTonTransactions();
      TonMonitorLogger.logMonitorCompleted();
    } catch (error) {
      TonMonitorLogger.logMonitorFailed({
        error,
        status: "monitor_failed",
      });
    }
  }
);
