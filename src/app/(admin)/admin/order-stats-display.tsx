'use client';

import type { LucideIcon } from 'lucide-react';
import {
  Bar<PERSON>hart3,
  CheckCircle,
  DollarSign,
  Gift,
  RefreshCw,
  ShoppingCart,
  XCircle,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import type { OrderStats } from '@/api/order-api';
import { getOrderStats } from '@/api/order-api';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

export const OrderStatsDisplay = () => {
  const { toast } = useToast();
  const [stats, setStats] = useState<OrderStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const loadOrderStats = useCallback(async () => {
    try {
      setIsLoading(true);
      const orderStats = await getOrderStats();
      setStats(orderStats);
    } catch (error) {
      console.error('Error loading order statistics:', error);
      toast({
        title: 'Error',
        description: 'Failed to load order statistics',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await loadOrderStats();
      toast({
        title: 'Success',
        description: 'Order statistics refreshed successfully',
      });
    } catch (error) {
      console.error('Error refreshing order statistics:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    loadOrderStats();
  }, [loadOrderStats]);

  const StatItem = ({
    title,
    value,
    icon: Icon,
    color = 'text-blue-500',
  }: {
    title: string;
    value: number;
    icon: LucideIcon;
    color?: string;
  }) => (
    <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
      <div className="flex items-center gap-3">
        <div className={`p-2 rounded-lg bg-card ${color}`}>
          <Icon className="h-4 w-4" />
        </div>
        <span className="text-sm font-medium text-muted-foreground">
          {title}
        </span>
      </div>
      <span className="text-lg font-bold text-foreground">
        {isLoading ? '...' : value.toLocaleString()}
      </span>
    </div>
  );

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-lg">
              <BarChart3 className="h-5 w-5" />
              Order Statistics
            </CardTitle>
            <CardDescription className="text-sm mt-1">
              Real-time order statistics across all statuses
            </CardDescription>
          </div>
          <Button
            onClick={handleRefresh}
            disabled={isRefreshing}
            size="sm"
            variant="outline"
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <StatItem
            title="Total Orders"
            value={stats?.totalOrders ?? 0}
            icon={ShoppingCart}
            color="text-primary"
          />
          <StatItem
            title="Fulfilled Orders"
            value={stats?.fulfilledOrders ?? 0}
            icon={CheckCircle}
            color="text-green-400"
          />
          <StatItem
            title="Sent to Bot"
            value={stats?.giftSentToRelayerOrders ?? 0}
            icon={Gift}
            color="text-purple-400"
          />
          <StatItem
            title="Cancelled Orders"
            value={stats?.cancelledOrders ?? 0}
            icon={XCircle}
            color="text-destructive"
          />
        </div>

        <div className="border-t pt-4">
          <h4 className="text-sm font-semibold text-muted-foreground mb-3">
            Paid Orders Breakdown
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <StatItem
              title="Without Secondary Price"
              value={stats?.paidOrdersWithoutSecondaryPrice ?? 0}
              icon={DollarSign}
              color="text-yellow-600"
            />
            <StatItem
              title="With Secondary Price"
              value={stats?.paidOrdersWithSecondaryPrice ?? 0}
              icon={DollarSign}
              color="text-orange-600"
            />
          </div>
        </div>

        {stats && (
          <div className="border-t pt-4">
            <h4 className="text-sm font-semibold text-muted-foreground mb-3">
              Summary
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center p-3 rounded-lg bg-primary/10">
                <div className="text-lg font-bold text-primary">
                  {(
                    stats.paidOrdersWithoutSecondaryPrice +
                    stats.paidOrdersWithSecondaryPrice
                  ).toLocaleString()}
                </div>
                <div className="text-muted-foreground">Total Paid</div>
              </div>
              <div className="text-center p-3 rounded-lg bg-green-500/10">
                <div className="text-lg font-bold text-green-400">
                  {stats.totalOrders > 0
                    ? `${(
                        (stats.fulfilledOrders / stats.totalOrders) *
                        100
                      ).toFixed(1)}%`
                    : '0%'}
                </div>
                <div className="text-muted-foreground">Completion Rate</div>
              </div>
              <div className="text-center p-3 rounded-lg bg-muted/50">
                <div className="text-lg font-bold text-muted-foreground">
                  {(
                    stats.totalOrders -
                    stats.paidOrdersWithoutSecondaryPrice -
                    stats.paidOrdersWithSecondaryPrice -
                    stats.giftSentToRelayerOrders -
                    stats.fulfilledOrders -
                    stats.cancelledOrders
                  ).toLocaleString()}
                </div>
                <div className="text-muted-foreground">Active Orders</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
