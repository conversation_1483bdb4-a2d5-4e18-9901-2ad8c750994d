'use client';

import { useAdminAuth } from '@/hooks/use-admin-auth';

import { CustomReferralManagement } from '../custom-referral-management';

export default function ReferralsPage() {
  const { isAuthorized } = useAdminAuth();

  if (!isAuthorized) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Custom Referrals</h1>
        <p className="text-muted-foreground">
          Manage custom referral campaigns and bonuses
        </p>
      </div>

      <CustomReferralManagement />
    </div>
  );
}
