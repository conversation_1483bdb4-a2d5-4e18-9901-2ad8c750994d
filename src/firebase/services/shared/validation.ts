export function validateOrderId(orderId?: string): void {
  if (!orderId) {
    throw new Error("Order ID is required");
  }
}

export function validateTelegramId(tgId?: string): void {
  if (!tgId) {
    throw new Error("Telegram ID is required");
  }
}

export function validateGiftId(giftId?: string): void {
  if (!giftId) {
    throw new Error("Gift ID is required");
  }
}

export function validateUserId(userId?: string): void {
  if (!userId) {
    throw new Error("User ID is required");
  }
}
