import * as admin from "firebase-admin";
import {
  APP_USERS_COLLECTION,
  UserEntity,
} from "../mikerudenko/marketplace-shared";

export interface UserLookupParams {
  userId?: string;
  tgId?: string;
}

export interface UserLookupResult {
  success: boolean;
  userId?: string;
  message?: string;
}

export async function getUserById(userId: string) {
  const db = admin.firestore();
  const userDoc = await db.collection(APP_USERS_COLLECTION).doc(userId).get();

  if (!userDoc.exists) {
    return null;
  }

  return { id: userDoc.id, ...userDoc.data() } as UserEntity;
}
