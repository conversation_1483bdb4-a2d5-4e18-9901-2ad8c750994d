import { log } from "../../utils/logger";
import { LogOperations } from "../../constants/bot-commands";

export const GetCancelledGiftFlowLogger = {
  logCancelledGiftProcessingStarted(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.info("Processing cancelled gift retrieval", {
      operation: LogOperations.GET_CANCELLED_GIFT,
      ...params,
    });
  },

  logOrderNotFoundForCancelledGift(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.warn("Order not found for cancelled gift retrieval", {
      operation: LogOperations.GET_CANCELLED_GIFT,
      ...params,
    });
  },

  logOrderNotCancelled(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    orderStatus: string;
  }) {
    log.info("Order is not cancelled, cannot retrieve gift", {
      operation: LogOperations.GET_CANCELLED_GIFT,
      ...params,
    });
  },

  logUserNotAuthorizedForCancelledGift(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    originalSellerId?: string;
  }) {
    log.warn("User not authorized - not original seller for cancelled gift", {
      operation: LogOperations.GET_CANCELLED_GIFT,
      ...params,
    });
  },

  logNoGiftToTransferBack(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.warn("No gift available to transfer back for cancelled order", {
      operation: LogOperations.GET_CANCELLED_GIFT,
      ...params,
    });
  },

  logMissingBusinessConnectionForCancelledGift(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.error("Missing business connection ID for cancelled gift transfer", {
      operation: LogOperations.GET_CANCELLED_GIFT,
      ...params,
    });
  },

  logCancelledGiftTransferStarted(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    businessConnectionId: string;
    giftToTransferBack: string;
  }) {
    log.info("Starting cancelled gift transfer back to seller", {
      operation: LogOperations.GET_CANCELLED_GIFT,
      ...params,
    });
  },

  logGiftFieldResetSuccess(params: { orderId: string }) {
    log.info("Gift field reset successfully for cancelled order", {
      operation: LogOperations.GET_CANCELLED_GIFT,
      ...params,
    });
  },

  logGiftFieldResetError(params: { orderId: string; error: unknown }) {
    log.error("Failed to reset gift field for cancelled order", params.error, {
      operation: LogOperations.GET_CANCELLED_GIFT,
      orderId: params.orderId,
    });
  },

  logCancelledGiftTransferCompleted(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.info("Cancelled gift transfer completed successfully", {
      operation: LogOperations.GET_CANCELLED_GIFT,
      ...params,
    });
  },
};
