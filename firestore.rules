rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    function isAdmin() {
      let userDoc = /databases/$(database)/documents/users/$(request.auth.uid);
      return request.auth != null && exists(userDoc) && get(userDoc).data.role == 'admin';
    }

    match /users/{userId} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }

    match /collections/{document=**} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }
    
    match /app_config/{document=**} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }
    
    match /orders/{document=**} {
      allow read: if true; // Everyone can view
      allow create, update, delete: if isAdmin();
    }

    match /counters/{document=**} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }

    match /bot_sessions/{document=**} {
      allow read, write, delete: if isAdmin();
    }

    // User transaction history subcollections
    match /users/{userId}/tx_history/{document=**} {
      allow read: if request.auth != null && (request.auth.uid == userId || isAdmin());
      allow create, update, delete: if isAdmin();
    }

    // Order resell transaction history subcollections
    match /orders/{orderId}/resell_tx_history/{document=**} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }

    match /orders/{orderId}/proposal_prices/{document=**} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }

    match /gifts/{document=**} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }
  }
}