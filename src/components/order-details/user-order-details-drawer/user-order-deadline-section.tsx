import { Clock } from 'lucide-react';
import { useIntl } from 'react-intl';

import type { OrderEntity, UserType } from '@/mikerudenko/marketplace-shared';
import {
  getDeadlineDescription,
  getDeadlineTitle,
} from '@/services/order-service';

import { userOrderDeadlineSectionMessages } from './intl/user-order-deadline-section.messages';

interface UserOrderDeadlineSectionProps {
  order: OrderEntity;
  userType: UserType;
  timeLeft: string;
}

function DeadlineContainer({ children }: { children: React.ReactNode }) {
  return (
    <div className="bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-2xl p-4">
      {children}
    </div>
  );
}

function DeadlineHeader({ title }: { title: string }) {
  return (
    <div className="flex items-center gap-2 mb-2">
      <Clock className="w-5 h-5 text-orange-400" />
      <span className="text-orange-400 font-semibold">{title}</span>
    </div>
  );
}

export function UserOrderDeadlineSection({
  order,
  userType,
  timeLeft,
}: UserOrderDeadlineSectionProps) {
  const intl = useIntl();
  const { formatMessage: t } = intl;

  if (!timeLeft) {
    return (
      <DeadlineContainer>
        <DeadlineHeader title={t(userOrderDeadlineSectionMessages.waiting)} />
        <div className="text-sm text-[#708499]">
          {t(userOrderDeadlineSectionMessages.giftWillBecomeTransferableSoon)}
        </div>
      </DeadlineContainer>
    );
  }

  const title = getDeadlineTitle(order, userType, intl);
  const description = getDeadlineDescription(order, userType, intl);

  return (
    <DeadlineContainer>
      <DeadlineHeader title={title} />
      <div className="text-center">
        <div className="text-3xl font-mono font-bold text-[#f5f5f5] mb-2">
          {timeLeft}
        </div>
        {description && (
          <div className="text-sm text-[#708499]">{description}</div>
        )}
      </div>
    </DeadlineContainer>
  );
}
