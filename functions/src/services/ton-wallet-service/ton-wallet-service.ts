import { mnemonicToPrivate<PERSON>ey } from "@ton/crypto";
import { SendMode, TonClient, WalletContractV5R1, internal } from "@ton/ton";
import { getMarketplaceWalletMnemonic, isDevelopment } from "../../config";
import { getHttpEndpoint } from "@orbs-network/ton-access";
import { logTonWalletServiceError } from "./ton-wallet-service.logger";

interface TonWalletState {
  client: TonClient | null;
  marketplaceWallet: WalletContractV5R1 | null;
  keyPair: { publicKey: Buffer; secretKey: Buffer } | null;
}

let walletState: TonWalletState = {
  client: null,
  marketplaceWallet: null,
  keyPair: null,
};

async function initializeTonWallet(): Promise<void> {
  if (
    walletState.client &&
    walletState.marketplaceWallet &&
    walletState.keyPair
  ) {
    return;
  }

  try {
    const network = isDevelopment() ? "testnet" : "mainnet";
    const endpoint = await getHttpEndpoint({ network });

    walletState.client = new TonClient({ endpoint });

    const marketplaceWalletMnemonic = getMarketplaceWalletMnemonic();
    walletState.keyPair = await mnemonicToPrivateKey(
      marketplaceWalletMnemonic.split(" ")
    );

    const workchain = 0;
    walletState.marketplaceWallet = WalletContractV5R1.create({
      workchain,
      publicKey: walletState.keyPair.publicKey,
    });
  } catch (error) {
    logTonWalletServiceError({
      error,
      operation: "initialize_wallet",
    });
    throw error;
  }
}

export async function sendTonTransfer(params: {
  amount: number;
  toAddress: string;
  body: string;
}): Promise<{
  success: boolean;
  transactionHash: string;
  message: string;
}> {
  try {
    await initializeTonWallet();

    if (
      !walletState.client ||
      !walletState.marketplaceWallet ||
      !walletState.keyPair
    ) {
      throw new Error("TON wallet service not properly initialized");
    }

    const marketplaceContract = walletState.client.open(
      walletState.marketplaceWallet
    );

    // Get sequence number for the transaction
    const seqno = await walletState.marketplaceWallet.getSeqno(
      walletState.client.provider(walletState.marketplaceWallet.address)
    );

    let transfer: any;

    try {
      transfer = marketplaceContract.createTransfer({
        seqno,
        sendMode: SendMode.PAY_GAS_SEPARATELY,
        secretKey: walletState.keyPair.secretKey,
        messages: [
          internal({
            value: params.amount.toString(),
            to: params.toAddress,
            body: params.body,
          }),
        ],
      });

      // Send the transaction
      await marketplaceContract.send(transfer);
    } catch (error) {
      if (!isDevelopment()) {
        logTonWalletServiceError({
          error,
          operation: "send_transfer",
          walletAddress: params.toAddress,
        });
        throw error;
      }
    }

    const transactionHash = transfer?.hash()?.toString("hex");

    return {
      success: true,
      transactionHash: transactionHash ?? "",
      message: `Successfully transferred ${params.amount} TON`,
    };
  } catch (error) {
    logTonWalletServiceError({
      error,
      operation: "send_ton_transfer",
      walletAddress: params.toAddress,
    });
    throw error;
  }
}

export async function sendWithdrawal(
  amount: number,
  userWalletAddress: string
) {
  return sendTonTransfer({
    amount,
    toAddress: userWalletAddress,
    body: "Withdrawal from marketplace",
  });
}

export async function sendRevenueTransfer(
  amount: number,
  johnDowWallet: string
) {
  return sendTonTransfer({
    amount,
    toAddress: johnDowWallet,
    body: "Revenue transfer - John Dow",
  });
}
