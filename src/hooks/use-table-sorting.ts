import { useCallback, useState } from 'react';

import type { SortDirection } from '@/components/ui/sortable-table-header';

export interface SortConfig {
  key: string;
  direction: SortDirection;
}

export interface UseTableSortingProps {
  defaultSortKey?: string;
  defaultSortDirection?: SortDirection;
  onSortChange?: (sortConfig: SortConfig | null) => void;
}

export interface UseTableSortingReturn {
  sortKey: string;
  sortDirection: SortDirection;
  handleSort: (key: string, direction: SortDirection) => void;
  getSortConfig: () => SortConfig | null;
  resetSort: () => void;
}

export function useTableSorting({
  defaultSortKey = '',
  defaultSortDirection = null,
  onSortChange,
}: UseTableSortingProps = {}): UseTableSortingReturn {
  const [sortKey, setSortKey] = useState<string>(defaultSortKey);
  const [sortDirection, setSortDirection] =
    useState<SortDirection>(defaultSortDirection);

  const handleSort = useCallback(
    (key: string, direction: SortDirection) => {
      setSortKey(key);
      setSortDirection(direction);

      const sortConfig = direction ? { key, direction } : null;
      onSortChange?.(sortConfig);
    },
    [onSortChange],
  );

  const getSortConfig = useCallback((): SortConfig | null => {
    return sortKey && sortDirection
      ? { key: sortKey, direction: sortDirection }
      : null;
  }, [sortKey, sortDirection]);

  const resetSort = useCallback(() => {
    setSortKey('');
    setSortDirection(null);
    onSortChange?.(null);
  }, [onSortChange]);

  return {
    sortKey,
    sortDirection,
    handleSort,
    getSortConfig,
    resetSort,
  };
}
