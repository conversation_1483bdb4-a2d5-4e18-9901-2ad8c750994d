'use client';

import { useAdminAuth } from '@/hooks/use-admin-auth';

import { CollectionManagement } from '../collection-management';

export default function CollectionsPage() {
  const { isAuthorized } = useAdminAuth();

  if (!isAuthorized) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Collections</h1>
        <p className="text-muted-foreground">
          Manage marketplace collections and their settings
        </p>
      </div>

      <CollectionManagement />
    </div>
  );
}
