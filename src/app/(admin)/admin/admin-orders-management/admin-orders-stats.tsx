import { RotateCcw } from 'lucide-react';

import type { AdminOrderStats } from '@/api/admin-api';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface StatRowProps {
  label: string;
  value: number;
  onUpdate?: () => void;
  isUpdating?: boolean;
}

function StatRow({ label, value, onUpdate, isUpdating = false }: StatRowProps) {
  return (
    <TableRow>
      <TableCell className="font-medium">{label}</TableCell>
      <TableCell className="text-right font-mono">{value}</TableCell>
      <TableCell className="text-right">
        {onUpdate && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onUpdate}
            disabled={isUpdating}
            className="h-6 w-6 p-0"
          >
            <RotateCcw
              className={`h-3 w-3 ${isUpdating ? 'animate-spin' : ''}`}
            />
          </Button>
        )}
      </TableCell>
    </TableRow>
  );
}

interface AdminOrdersStatsProps {
  stats: AdminOrderStats | null;
  onUpdateStat?: (statType: string) => void;
  updatingStats?: Set<string>;
}

export function AdminOrdersStats({
  stats,
  onUpdateStat,
  updatingStats = new Set(),
}: AdminOrdersStatsProps) {
  if (!stats) {
    return null;
  }

  const totalNonAdminOrders =
    stats.nonAdminOrders.active +
    stats.nonAdminOrders.paid +
    stats.nonAdminOrders.giftSentToRelayer +
    stats.nonAdminOrders.cancelled +
    stats.nonAdminOrders.fulfilled;

  const totalAdminOrders =
    stats.adminOrders.active +
    stats.adminOrders.paid +
    stats.adminOrders.giftSentToRelayer +
    stats.adminOrders.cancelled +
    stats.adminOrders.fulfilled;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Summary</TableHead>
              <TableHead className="text-right">Count</TableHead>
              <TableHead className="text-right w-12">Refresh</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <StatRow
              label="Total Orders"
              value={stats.totalOrders}
              onUpdate={() => onUpdateStat?.('totalOrders')}
              isUpdating={updatingStats.has('totalOrders')}
            />
            <StatRow
              label="Non-Admin Orders"
              value={totalNonAdminOrders}
              onUpdate={() => onUpdateStat?.('nonAdminOrders')}
              isUpdating={updatingStats.has('nonAdminOrders')}
            />
            <StatRow
              label="Admin Orders"
              value={totalAdminOrders}
              onUpdate={() => onUpdateStat?.('adminOrders')}
              isUpdating={updatingStats.has('adminOrders')}
            />
          </TableBody>
        </Table>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Non-Admin Orders</TableHead>
              <TableHead className="text-right">Count</TableHead>
              <TableHead className="text-right w-12">Refresh</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <StatRow
              label="Active"
              value={stats.nonAdminOrders.active}
              onUpdate={() => onUpdateStat?.('nonAdminActive')}
              isUpdating={updatingStats.has('nonAdminActive')}
            />
            <StatRow
              label="Paid"
              value={stats.nonAdminOrders.paid}
              onUpdate={() => onUpdateStat?.('nonAdminPaid')}
              isUpdating={updatingStats.has('nonAdminPaid')}
            />
            <StatRow
              label="Gift Sent"
              value={stats.nonAdminOrders.giftSentToRelayer}
              onUpdate={() => onUpdateStat?.('nonAdminGiftSent')}
              isUpdating={updatingStats.has('nonAdminGiftSent')}
            />
            <StatRow
              label="Cancelled"
              value={stats.nonAdminOrders.cancelled}
              onUpdate={() => onUpdateStat?.('nonAdminCancelled')}
              isUpdating={updatingStats.has('nonAdminCancelled')}
            />
            <StatRow
              label="Fulfilled"
              value={stats.nonAdminOrders.fulfilled}
              onUpdate={() => onUpdateStat?.('nonAdminFulfilled')}
              isUpdating={updatingStats.has('nonAdminFulfilled')}
            />
          </TableBody>
        </Table>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Admin Orders</TableHead>
              <TableHead className="text-right">Count</TableHead>
              <TableHead className="text-right w-12">Refresh</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <StatRow
              label="Active"
              value={stats.adminOrders.active}
              onUpdate={() => onUpdateStat?.('adminActive')}
              isUpdating={updatingStats.has('adminActive')}
            />
            <StatRow
              label="Paid"
              value={stats.adminOrders.paid}
              onUpdate={() => onUpdateStat?.('adminPaid')}
              isUpdating={updatingStats.has('adminPaid')}
            />
            <StatRow
              label="Gift Sent"
              value={stats.adminOrders.giftSentToRelayer}
              onUpdate={() => onUpdateStat?.('adminGiftSent')}
              isUpdating={updatingStats.has('adminGiftSent')}
            />
            <StatRow
              label="Cancelled"
              value={stats.adminOrders.cancelled}
              onUpdate={() => onUpdateStat?.('adminCancelled')}
              isUpdating={updatingStats.has('adminCancelled')}
            />
            <StatRow
              label="Fulfilled"
              value={stats.adminOrders.fulfilled}
              onUpdate={() => onUpdateStat?.('adminFulfilled')}
              isUpdating={updatingStats.has('adminFulfilled')}
            />
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
