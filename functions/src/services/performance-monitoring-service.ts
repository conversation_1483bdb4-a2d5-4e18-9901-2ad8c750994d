import * as admin from "firebase-admin";

interface PerformanceMetrics {
  functionName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

interface PerformanceStats {
  totalCalls: number;
  averageDuration: number;
  successRate: number;
  lastUpdated: number;
}

const performanceMetrics: Map<string, PerformanceMetrics[]> = new Map();
const METRICS_RETENTION_HOURS = 24;
const MAX_METRICS_PER_FUNCTION = 1000;

export class PerformanceMonitor {
  private functionName: string;
  private startTime: number;
  private metadata: Record<string, any>;

  constructor(functionName: string, metadata: Record<string, any> = {}) {
    this.functionName = functionName;
    this.startTime = Date.now();
    this.metadata = metadata;
  }

  end(success: boolean = true, errorMessage?: string): number {
    const endTime = Date.now();
    const duration = endTime - this.startTime;

    const metric: PerformanceMetrics = {
      functionName: this.functionName,
      startTime: this.startTime,
      endTime,
      duration,
      success,
      errorMessage,
      metadata: this.metadata,
    };

    this.recordMetric(metric);
    return duration;
  }

  private recordMetric(metric: PerformanceMetrics) {
    if (!performanceMetrics.has(this.functionName)) {
      performanceMetrics.set(this.functionName, []);
    }

    const metrics = performanceMetrics.get(this.functionName)!;
    metrics.push(metric);

    // Keep only recent metrics
    const cutoffTime = Date.now() - METRICS_RETENTION_HOURS * 60 * 60 * 1000;
    const recentMetrics = metrics.filter(m => m.startTime > cutoffTime);
    
    // Limit number of metrics per function
    if (recentMetrics.length > MAX_METRICS_PER_FUNCTION) {
      recentMetrics.splice(0, recentMetrics.length - MAX_METRICS_PER_FUNCTION);
    }

    performanceMetrics.set(this.functionName, recentMetrics);
  }
}

export function startPerformanceMonitoring(
  functionName: string,
  metadata: Record<string, any> = {}
): PerformanceMonitor {
  return new PerformanceMonitor(functionName, metadata);
}

export function getPerformanceStats(functionName: string): PerformanceStats | null {
  const metrics = performanceMetrics.get(functionName);
  if (!metrics || metrics.length === 0) {
    return null;
  }

  const totalCalls = metrics.length;
  const successfulCalls = metrics.filter(m => m.success).length;
  const totalDuration = metrics.reduce((sum, m) => sum + (m.duration || 0), 0);

  return {
    totalCalls,
    averageDuration: totalDuration / totalCalls,
    successRate: (successfulCalls / totalCalls) * 100,
    lastUpdated: Date.now(),
  };
}

export function getAllPerformanceStats(): Record<string, PerformanceStats> {
  const stats: Record<string, PerformanceStats> = {};
  
  for (const [functionName] of performanceMetrics) {
    const stat = getPerformanceStats(functionName);
    if (stat) {
      stats[functionName] = stat;
    }
  }

  return stats;
}

export function logPerformanceWarning(functionName: string, duration: number, threshold: number = 5000) {
  if (duration > threshold) {
    console.warn(`Performance Warning: ${functionName} took ${duration}ms (threshold: ${threshold}ms)`);
  }
}

// Helper function to wrap async functions with performance monitoring
export function withPerformanceMonitoring<T extends any[], R>(
  functionName: string,
  fn: (...args: T) => Promise<R>,
  metadata: Record<string, any> = {}
) {
  return async (...args: T): Promise<R> => {
    const monitor = startPerformanceMonitoring(functionName, metadata);
    
    try {
      const result = await fn(...args);
      const duration = monitor.end(true);
      logPerformanceWarning(functionName, duration);
      return result;
    } catch (error) {
      const duration = monitor.end(false, error instanceof Error ? error.message : 'Unknown error');
      logPerformanceWarning(functionName, duration);
      throw error;
    }
  };
}

// Cloud Function to get performance metrics (for admin monitoring)
export async function getPerformanceMetrics(): Promise<Record<string, PerformanceStats>> {
  return getAllPerformanceStats();
}

// Cleanup old metrics periodically
export function cleanupOldMetrics() {
  const cutoffTime = Date.now() - METRICS_RETENTION_HOURS * 60 * 60 * 1000;
  
  for (const [functionName, metrics] of performanceMetrics) {
    const recentMetrics = metrics.filter(m => m.startTime > cutoffTime);
    if (recentMetrics.length !== metrics.length) {
      performanceMetrics.set(functionName, recentMetrics);
    }
  }
}

// Auto-cleanup every hour
setInterval(cleanupOldMetrics, 60 * 60 * 1000);
