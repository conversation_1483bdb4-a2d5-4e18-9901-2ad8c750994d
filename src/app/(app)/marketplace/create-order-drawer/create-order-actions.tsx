'use client';

import { useIntl } from 'react-intl';

import { ConfirmWrapper } from '@/components/shared/confirm-wrapper';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';

import { createOrderDrawerMessages } from './intl/create-order-drawer.messages';

interface CreateOrderActionsProps {
  onCreateOrder: () => void;
  onCancel: () => void;
  isValidPrice: boolean;
  hasSufficientBalance: boolean;
  loading: boolean;
  price: number;
}

export function CreateOrderActions({
  onCreateOrder,
  onCancel,
  isValidPrice,
  hasSufficientBalance,
  loading,
  price,
}: CreateOrderActionsProps) {
  const { formatMessage: t } = useIntl();

  const isDisabled = !isValidPrice || !hasSufficientBalance || loading;

  return (
    <div className="space-y-3 pt-4">
      <ConfirmWrapper>
        <Button
          onClick={onCreateOrder}
          disabled={isDisabled}
          className="w-full h-12 bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white border-0 rounded-2xl"
        >
          {loading ? (
            t(createOrderDrawerMessages.creating)
          ) : (
            <>
              {t(createOrderDrawerMessages.create)}
              {isValidPrice && (
                <>
                  {' '}
                  &#40;{price.toFixed(2)} <TonLogo className="-m-2" size={24} />
                  <span className="-ml-1">&#41;</span>
                </>
              )}
            </>
          )}
        </Button>
      </ConfirmWrapper>

      <Button
        variant="outline"
        onClick={onCancel}
        className="w-full h-12 bg-transparent border-[#3a4a5c]/50 text-[#708499] hover:bg-[#232e3c]/50 hover:text-[#f5f5f5] rounded-2xl"
        disabled={loading}
      >
        {t(createOrderDrawerMessages.cancel)}
      </Button>
    </div>
  );
}
