'use client';

import React from 'react';

import type { TabType } from '@/app/(app)/marketplace/use-marketplace-orders';
import { UserOrderCard } from '@/app/(app)/orders/user-order-card';
import { BaseOrderCard } from '@/components/shared/base-order-card';
import { GridItem } from '@/components/ui/virtualized-grid';
import type {
  CollectionEntity,
  OrderEntity,
  UserType,
} from '@/mikerudenko/marketplace-shared';

export type CardVariant = 'order' | 'user-order';

interface BaseVirtualizedCardProps {
  order: OrderEntity;
  onClick: () => void;
  index: number;
  initialRenderedCount?: number;
  variant: CardVariant;
}

interface OrderCardVariantProps extends BaseVirtualizedCardProps {
  variant: 'order';
  collection: CollectionEntity | undefined;
  activeTab?: TabType;
}

interface UserOrderCardVariantProps extends BaseVirtualizedCardProps {
  variant: 'user-order';
  userRole: UserType;
  onSendAGiftClick?: () => void;
  onGetAGiftClick?: () => void;
  onResellOrder?: () => void;
  onActivateOrder?: () => void;
  onGetCancelledGift?: () => void;
  onAttachGiftClick?: () => void;
}

export type VirtualizedCardProps =
  | OrderCardVariantProps
  | UserOrderCardVariantProps;

export const VirtualizedCard = (props: VirtualizedCardProps) => {
  const { order, onClick, index, initialRenderedCount = 15, variant } = props;

  const getItemId = () => {
    switch (variant) {
      case 'order':
        return `order-${order.id}`;
      case 'user-order':
        return `user-order-${order.id}`;
      default:
        return `card-never`;
    }
  };

  const renderCard = () => {
    switch (variant) {
      case 'order': {
        const orderProps = props;
        return (
          <BaseOrderCard
            collection={orderProps.collection}
            onClick={onClick}
            order={order}
            activeTab={
              orderProps.activeTab === 'activity'
                ? undefined
                : orderProps.activeTab
            }
            showButton={true}
          />
        );
      }

      case 'user-order': {
        const userOrderProps = props;
        return (
          <UserOrderCard
            userType={userOrderProps.userRole}
            {...{
              order,
              onClick,
            }}
            onSendAGiftClick={userOrderProps.onSendAGiftClick}
            onGetAGiftClick={userOrderProps.onGetAGiftClick}
            onResellOrder={userOrderProps.onResellOrder}
            onActivateOrder={userOrderProps.onActivateOrder}
            onGetCancelledGift={userOrderProps.onGetCancelledGift}
            onAttachGiftClick={userOrderProps.onAttachGiftClick}
          />
        );
      }

      default:
        return null;
    }
  };

  return (
    <GridItem
      itemId={getItemId()}
      {...{
        index,
        initialRenderedCount,
      }}
    >
      {renderCard()}
    </GridItem>
  );
};
