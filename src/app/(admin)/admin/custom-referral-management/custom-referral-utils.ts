export const formatBpsToPercent = (bps: number): string => {
  return (bps / 100).toFixed(2);
};

export const getCustomReferralWarnings = (
  referralFee: number,
  maxPurchaseFee: number,
): string[] => {
  const warnings: string[] = [];

  if (referralFee > maxPurchaseFee) {
    warnings.push(
      `Custom referral fee (${formatBpsToPercent(referralFee)}%) exceeds purchase fee (${formatBpsToPercent(maxPurchaseFee)}%). Note: Referral fees only apply to purchase fees, not resell fees.`,
    );
  }

  return warnings;
};
