import { useIntl } from 'react-intl';

import { CollectionStatus } from '@/mikerudenko/marketplace-shared';
import { collectionStatusMessages } from '@/utils/intl/collection-status.messages';

export const useCollectionStatusText = () => {
  const intl = useIntl();

  const collectionStatusMap = new Map([
    [
      CollectionStatus.PREMARKET,
      intl.formatMessage(collectionStatusMessages.premarket),
    ],
    [
      CollectionStatus.MARKET,
      intl.formatMessage(collectionStatusMessages.market),
    ],
    [
      CollectionStatus.DELETED,
      intl.formatMessage(collectionStatusMessages.deleted),
    ],
  ]);

  return (status: CollectionStatus) => {
    return collectionStatusMap.get(status) ?? status;
  };
};
