export function throwTelegramClientError(message?: string): never {
  throw new Error(message ?? "Telegram client error");
}

export function throwCollectionUpdateError(message?: string): never {
  throw new Error(message ?? "Error updating collection");
}

export function throwLimitedCollectionsMonitorError(message?: string): never {
  throw new Error(
    message ?? "Error occurred while monitoring limited collections"
  );
}
