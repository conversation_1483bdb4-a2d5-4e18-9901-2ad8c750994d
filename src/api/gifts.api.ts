import {
  and,
  collection,
  doc,
  getDoc,
  getDocs,
  or,
  query,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import { AppCloudFunctions } from '@/core.constants';
import {
  type GiftEntity,
  GIFTS_COLLECTION_NAME,
  GiftStatus,
  type OrderEntity,
  ORDERS_COLLECTION_NAME,
  OrderStatus,
} from '@/mikerudenko/marketplace-shared';
import { firebaseFunctions, firestore } from '@/root-context';

export async function getGiftById(giftId: string) {
  if (!giftId) {
    return null;
  }

  try {
    const giftRef = doc(firestore, GIFTS_COLLECTION_NAME, giftId);
    const giftSnap = await getDoc(giftRef);

    if (!giftSnap.exists()) {
      return null;
    }

    return {
      id: giftSnap.id,
      ...giftSnap.data(),
    } as GiftEntity;
  } catch (error) {
    console.error('Error fetching gift by ID:', error);
    return null;
  }
}

export async function getUserGifts(telegramId: string): Promise<GiftEntity[]> {
  if (!telegramId) {
    return [];
  }

  try {
    const giftsQuery = query(
      collection(firestore, GIFTS_COLLECTION_NAME),
      where('owner_tg_id', '==', telegramId),
      where('status', '==', GiftStatus.DEPOSITED),
    );

    const snapshot = await getDocs(giftsQuery);

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as GiftEntity[];
  } catch (error) {
    console.error('Error fetching user gifts:', error);
    return [];
  }
}

export async function getUserGiftOrders(
  userId: string,
): Promise<OrderEntity[]> {
  if (!userId) {
    return [];
  }

  try {
    const ordersQuery = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      where('giftId', '!=', null),
      where('status', 'in', [
        OrderStatus.GIFT_SENT_TO_RELAYER,
        OrderStatus.ACTIVE,
      ]),
    );

    const snapshot = await getDocs(ordersQuery);

    const orders = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as OrderEntity[];

    // augment-refactor instead of client filtering use collection group query for this complex request
    return orders.filter((order) => {
      const isBuyer =
        order.buyerId === userId &&
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER;
      const isSeller =
        order.sellerId === userId && order.status === OrderStatus.ACTIVE;

      return isBuyer || isSeller;
    });
  } catch (error) {
    console.error('Error fetching user gift orders:', error);
    return [];
  }
}

export async function getEligibleOrdersForGiftLinking(
  userId: string,
  collectionId: string,
): Promise<OrderEntity[]> {
  try {
    const ordersQuery = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      where('collectionId', '==', collectionId),
      where('sellerId', '==', userId),
      where('status', 'in', [OrderStatus.CREATED, OrderStatus.PAID]),
    );

    const querySnapshot = await getDocs(ordersQuery);
    const orders: OrderEntity[] = [];

    querySnapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    return orders;
  } catch (error) {
    console.error('Error fetching eligible orders for gift linking:', error);
    return [];
  }
}

export async function getGiftsAvailableForLinkingToOrder(
  telegramId: string,
  collectionId: string,
): Promise<GiftEntity[]> {
  if (!telegramId || !collectionId) {
    return [];
  }

  try {
    // 1. Get user's deposited gifts for the specific collection
    const giftsQuery = query(
      collection(firestore, GIFTS_COLLECTION_NAME),
      where('owner_tg_id', '==', telegramId),
      where('collectionId', '==', collectionId),
      where('status', '==', GiftStatus.DEPOSITED),
    );

    const giftsSnapshot = await getDocs(giftsQuery);
    const gifts = giftsSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as GiftEntity[];

    if (gifts.length === 0) {
      return [];
    }

    // 2. Get all gift IDs to check in a single query
    const giftIds = gifts.map((gift) => gift.id!);

    // 3. Find orders that use any of these gifts with specific statuses
    const ordersQuery = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      and(
        where('giftId', 'in', giftIds),
        or(
          where('status', '==', OrderStatus.PAID),
          where('status', '==', OrderStatus.ACTIVE),
          where('status', '==', OrderStatus.GIFT_SENT_TO_RELAYER),
        ),
      ),
    );

    const ordersSnapshot = await getDocs(ordersQuery);

    // 4. Create a set of used gift IDs for fast lookup
    const usedGiftIds = new Set<string>();
    ordersSnapshot.docs.forEach((doc) => {
      const orderData = doc.data();
      if (orderData.giftId) {
        usedGiftIds.add(orderData.giftId);
      }
    });

    // 5. Filter out gifts that are already used
    const availableGifts = gifts.filter((gift) => !usedGiftIds.has(gift.id!));

    return availableGifts;
  } catch (error) {
    console.error(
      'Error fetching gifts available for linking to order:',
      error,
    );
    return [];
  }
}

interface CreateSellOrderFromGiftRequest {
  giftId: string;
  price: number;
}

interface CreateSellOrderFromGiftResponse {
  success: boolean;
  message: string;
  order?: OrderEntity;
}

export async function createSellOrderFromGift(
  giftId: string,
  price: number,
): Promise<CreateSellOrderFromGiftResponse> {
  try {
    const createSellOrderFromGiftFunction = httpsCallable<
      CreateSellOrderFromGiftRequest,
      CreateSellOrderFromGiftResponse
    >(firebaseFunctions, AppCloudFunctions.createSellOrderFromGift);

    const result = await createSellOrderFromGiftFunction({
      giftId,
      price,
    });

    return result.data;
  } catch (error) {
    console.error('Error creating sell order from gift:', error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
