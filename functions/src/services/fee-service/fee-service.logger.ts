import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const feeServiceLogger = createServiceLogger("fee-service");

export const FeeServiceLogger = {
  logAppConfigNotFound() {
    feeServiceLogger.logInfo(
      "App config not found, using zero fees",
      LogOperations.APP_CONFIG_FETCH,
      {}
    );
  },

  logAdminUserNotFound() {
    feeServiceLogger.logInfo(
      "No admin user found",
      LogOperations.ADMIN_USER_LOOKUP,
      {}
    );
  },

  logFeeApplied({
    feeAmount,
    feeType,
    userId,
    netAmount,
  }: {
    feeAmount: number;
    feeType: string;
    userId?: string;
    netAmount?: number;
  }) {
    feeServiceLogger.logInfo(
      `${feeType} fee applied`,
      LogOperations.FEE_APPLIED,
      {
        feeAmount,
        feeType,
        userId,
        netAmount,
      }
    );
  },

  logReferralFeeApplied({
    feeAmount,
    feeType,
    referrerFeeRate,
    referrerId,
    referralId,
  }: {
    feeAmount: number;
    feeType: string;
    referrerFeeRate: number;
    referrerId: string;
    referralId: string;
  }) {
    feeServiceLogger.logInfo(
      "Purchase referral fee applied",
      LogOperations.REFERRAL_FEE,
      {
        feeAmount,
        feeType,
        referrerFeeRate,
        referrerId,
        referralId,
      }
    );
  },

  logCustomReferralFee({
    referrerFeeRate,
    referrerId,
  }: {
    referrerFeeRate: number;
    referrerId: string;
  }) {
    feeServiceLogger.logInfo(
      "Using custom referral fee",
      LogOperations.CUSTOM_REFERRAL,
      {
        referrerFeeRate,
        referrerId,
      }
    );
  },

  logOrderReferralFee({
    referrerFeeRate,
    referrerId,
  }: {
    referrerFeeRate: number;
    referrerId: string;
  }) {
    feeServiceLogger.logInfo(
      "Using order referral fee",
      LogOperations.ORDER_REFERRAL,
      {
        referrerFeeRate,
        referrerId,
      }
    );
  },

  logReferrerNotFound({ referralId }: { referralId: string }) {
    feeServiceLogger.logInfo(
      "Referrer with tg_id not found, adding full fee to marketplace",
      LogOperations.REFERRER_NOT_FOUND,
      { referralId }
    );
  },

  logTotalFeeApplied({
    feeAmount,
    feeType,
    referralFee,
    marketplaceFee,
  }: {
    feeAmount: number;
    feeType: string;
    referralFee: number;
    marketplaceFee: number;
  }) {
    feeServiceLogger.logInfo(
      `${feeType} fee applied`,
      LogOperations.TOTAL_FEE,
      {
        feeAmount,
        feeType,
        referralFee,
        marketplaceFee,
      }
    );
  },

  logFeeServiceError({
    error,
    operation,
    userId,
    amount,
  }: {
    error: unknown;
    operation: LogOperations;
    userId?: string;
    amount?: number;
  }) {
    feeServiceLogger.logError(
      `Error in fee service: ${operation}`,
      error,
      operation,
      {
        userId,
        amount,
      }
    );
  },
};
