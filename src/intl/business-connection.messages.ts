import { defineMessages } from "@formatjs/intl";

export const businessConnectionMessages = defineMessages({
  giftReadyForBuyer: {
    id: "businessConnection.giftReadyForBuyer",
    defaultMessage:
      "🎁 Great news! Your gift for order #{orderNumber} is ready for delivery. Please check your orders.",
  },
  giftSentError: {
    id: "businessConnection.giftSentError",
    defaultMessage: "❌ Failed to send gift to relayer. Please try again.",
  },
  giftSentSuccess: {
    id: "businessConnection.giftSentSuccess",
    defaultMessage:
      "✅ Gift successfully sent to relayer! The buyer will be notified.",
  },
  giftTransferGenericError: {
    id: "businessConnection.giftTransferGenericError",
    defaultMessage: "❌ Failed to transfer gift. Please try again.",
  },
  giftTransferredSuccess: {
    id: "businessConnection.giftTransferredSuccess",
    defaultMessage: "✅ Gift successfully transferred!",
  },
  incorrectGift: {
    id: "businessConnection.incorrectGift",
    defaultMessage:
      "❌ This gift does not match the order requirements. Please send the correct gift.",
  },
  noGiftToTransfer: {
    id: "businessConnection.noGiftToTransfer",
    defaultMessage: "❌ No gift found to transfer.",
  },
  orderNotFound: {
    id: "businessConnection.orderNotFound",
    defaultMessage:
      "❌ Order not found. Please check the order ID and try again.",
  },
  processingGift: {
    id: "businessConnection.processingGift",
    defaultMessage: "⏳ Processing your gift...",
  },
  processingWithdrawal: {
    id: "businessConnection.processingWithdrawal",
    defaultMessage: "⏳ Processing your withdrawal...",
  },
  withdrawalError: {
    id: "businessConnection.withdrawalError",
    defaultMessage: "❌ Failed to withdraw gift. Please try again.",
  },
  withdrawalSuccess: {
    id: "businessConnection.withdrawalSuccess",
    defaultMessage: "✅ Gift successfully withdrawn!",
  },
});
