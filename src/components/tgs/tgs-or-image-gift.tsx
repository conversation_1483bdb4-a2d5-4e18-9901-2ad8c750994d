'use client';

import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

import { TgsSkeleton } from '@/components/tgs/tgs-skeleton';
import type { OrderGift } from '@/mikerudenko/marketplace-shared';
import {
  AssetFallbackState,
  generateGiftBackground,
  generateGiftColorFilter,
  generateGiftPatternArray,
  getGiftAssetUrl,
  type PatternPosition,
} from '@/services/gift-image-service';
import {
  areGiftAssetsPreloaded,
  cacheGiftAssets,
  getCachedGiftAssets,
} from '@/utils/gift-cache-utils';

const TgsViewer = dynamic(
  () => import('./tgs-viewer').then((mod) => ({ default: mod.TgsViewer })),
  {
    ssr: false,
    loading: () => (
      <TgsSkeleton
        className="w-full h-full"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
        }}
      />
    ),
  },
);

interface TgsOrImageGiftProps {
  isImage: boolean;
  gift: OrderGift;
  className?: string;
  style?: React.CSSProperties;
}

function useGiftAssets(gift: OrderGift, isImage: boolean) {
  const [modelState, setModelState] = useState<AssetFallbackState>(
    isImage ? AssetFallbackState.PNG : AssetFallbackState.TGS,
  );
  const [patternState, setPatternState] = useState<AssetFallbackState>(
    isImage ? AssetFallbackState.PNG : AssetFallbackState.TGS,
  );
  const [modelSrc, setModelSrc] = useState<string>('');
  const [patternSrc, setPatternSrc] = useState<string>('');
  const [modelLoading, setModelLoading] = useState<boolean>(false);
  const [modelImageLoaded, setModelImageLoaded] = useState<boolean>(false);
  const [showSkeleton, setShowSkeleton] = useState<boolean>(false);
  const [patternsLoaded, setPatternsLoaded] = useState<number>(0);
  const [totalPatterns, setTotalPatterns] = useState<number>(0);

  useEffect(() => {
    if (!gift || !gift.model?.name || !gift.symbol?.name || !gift.base_name) {
      setModelLoading(false);
      setModelImageLoaded(false);
      setPatternsLoaded(0);
      setTotalPatterns(0);
      setModelSrc('');
      setPatternSrc('');
      return;
    }

    // Set total patterns count from the pattern array
    const patternArray = generateGiftPatternArray();
    setTotalPatterns(patternArray.length);
    setPatternsLoaded(0);

    // Check if assets are already cached
    const cachedAssets = getCachedGiftAssets(gift, isImage);
    const assetsPreloaded = areGiftAssetsPreloaded(gift, isImage);

    const initialState = isImage
      ? AssetFallbackState.PNG
      : AssetFallbackState.TGS;
    setModelState(initialState);
    setPatternState(initialState);

    let modelUrl: string;
    let patternUrl: string;

    if (cachedAssets) {
      modelUrl = cachedAssets.modelUrl;
      patternUrl = cachedAssets.patternUrl;
    } else {
      modelUrl = getGiftAssetUrl(
        'models',
        gift.model.name,
        initialState,
        gift.base_name,
      );
      patternUrl = getGiftAssetUrl(
        'patterns',
        gift.symbol.name,
        initialState,
        gift.base_name,
      );
      // Cache the URLs for future use
      cacheGiftAssets(gift, isImage);
    }

    setModelSrc(modelUrl);
    setPatternSrc(patternUrl);

    // Always show skeleton initially when loading new assets
    setShowSkeleton(true);
    setModelImageLoaded(false);

    // Only set loading to true if assets aren't preloaded
    if (modelUrl && !assetsPreloaded) {
      setModelLoading(true);
    }

    // Fallback timeout to hide skeleton in case load events don't fire
    const timer = setTimeout(() => {
      setShowSkeleton(false);
      setModelLoading(false);
    }, 0);

    return () => clearTimeout(timer);
  }, [gift, isImage]);

  // Handle loading states based on asset states
  useEffect(() => {
    // If model state is FAILED or we don't have a model source, stop loading
    if (modelState === AssetFallbackState.FAILED || !modelSrc) {
      setModelLoading(false);
    }
  }, [modelState, modelSrc]);

  const handleModelError = () => {
    setModelImageLoaded(false);
    if (isImage || !gift?.model?.name || !gift?.base_name) {
      setModelState(AssetFallbackState.FAILED);
      setModelLoading(false);
      return;
    }

    switch (modelState) {
      case AssetFallbackState.TGS:
        setModelState(AssetFallbackState.PNG);
        setModelSrc(
          getGiftAssetUrl(
            'models',
            gift.model.name,
            AssetFallbackState.PNG,
            gift.base_name,
          ),
        );
        break;
      case AssetFallbackState.PNG:
        setModelState(AssetFallbackState.FAILED);
        setModelLoading(false);
        break;
    }
  };

  const handleModelLoad = useCallback(() => {
    setModelLoading(false);
    setModelImageLoaded(true);
    // Hide skeleton when model is loaded AND all patterns are loaded
    if (patternsLoaded >= totalPatterns) {
      setShowSkeleton(false);
    }
  }, [patternsLoaded, totalPatterns]);

  const handlePatternError = () => {
    if (isImage || !gift?.symbol?.name || !gift?.base_name) {
      setPatternState(AssetFallbackState.FAILED);
      return;
    }

    switch (patternState) {
      case AssetFallbackState.TGS:
        setPatternState(AssetFallbackState.PNG);
        setPatternSrc(
          getGiftAssetUrl(
            'patterns',
            gift.symbol.name,
            AssetFallbackState.PNG,
            gift.base_name,
          ),
        );
        break;
      case AssetFallbackState.PNG:
        setPatternState(AssetFallbackState.FAILED);
        break;
    }
  };

  const handlePatternLoad = useCallback(() => {
    setPatternsLoaded((prev) => {
      const newCount = prev + 1;
      // Hide skeleton when ALL patterns are loaded AND model is loaded
      if (!modelLoading && newCount >= totalPatterns) {
        setShowSkeleton(false);
      }
      return newCount;
    });
  }, [modelLoading, totalPatterns]);

  return {
    modelSrc,
    patternSrc,
    modelState,
    patternState,
    modelLoading,
    modelImageLoaded,
    showSkeleton,
    patternsLoaded,
    totalPatterns,
    handleModelError,
    handlePatternError,
    handleModelLoad,
    handlePatternLoad,
  };
}

// Global cache for shared pattern images to prevent multiple loads
const sharedPatternCache = new Map<
  string,
  {
    imageLoaded: boolean;
    loading: boolean;
    error: boolean;
    subscribers: Set<
      (data: { imageLoaded: boolean; loading: boolean; error: boolean }) => void
    >;
  }
>();

// Hook to manage shared PNG pattern image with global deduplication
function useSharedPatternImage(gift: OrderGift) {
  const [patternSrc, setPatternSrc] = useState<string>('');
  const [patternLoading, setPatternLoading] = useState<boolean>(false);
  const [patternLoaded, setPatternLoaded] = useState<boolean>(false);

  useEffect(() => {
    if (!gift?.symbol?.name || !gift?.base_name) {
      setPatternLoaded(false);
      setPatternLoading(false);
      return;
    }

    // Always use PNG for patterns to optimize loading
    const patternUrl = getGiftAssetUrl(
      'patterns',
      gift.symbol.name,
      AssetFallbackState.PNG,
      gift.base_name,
    );
    setPatternSrc(patternUrl);

    // Create cache key
    const cacheKey = `${gift.base_name}-${gift.symbol.name}`;

    // Check if already in global cache
    let cacheEntry = sharedPatternCache.get(cacheKey);

    if (!cacheEntry) {
      // Create new cache entry
      cacheEntry = {
        imageLoaded: false,
        loading: false,
        error: false,
        subscribers: new Set(),
      };
      sharedPatternCache.set(cacheKey, cacheEntry);
    }

    // Subscribe to cache updates
    const updateState = (data: {
      imageLoaded: boolean;
      loading: boolean;
      error: boolean;
    }) => {
      setPatternLoaded(data.imageLoaded);
      setPatternLoading(data.loading);
    };

    cacheEntry.subscribers.add(updateState);

    // If already loaded, use cached state
    if (cacheEntry.imageLoaded) {
      setPatternLoaded(true);
      setPatternLoading(false);
      console.log(
        '✅ Shared PNG pattern already loaded (single load):',
        patternUrl,
      );
      return () => {
        cacheEntry?.subscribers.delete(updateState);
      };
    }

    // If already loading, just wait
    if (cacheEntry.loading) {
      setPatternLoading(true);
      return () => {
        cacheEntry?.subscribers.delete(updateState);
      };
    }

    // Start loading
    cacheEntry.loading = true;
    setPatternLoading(true);

    // Notify all subscribers that loading started
    cacheEntry.subscribers.forEach((subscriber) => {
      subscriber({ imageLoaded: false, loading: true, error: false });
    });

    // Preload the image to ensure it's cached by the browser
    const img = new window.Image();
    img.onload = () => {
      console.log(
        '✅ Shared PNG pattern loaded and cached (single load):',
        patternUrl,
      );
      if (cacheEntry) {
        cacheEntry.imageLoaded = true;
        cacheEntry.loading = false;

        // Notify all subscribers
        cacheEntry.subscribers.forEach((subscriber) => {
          subscriber({ imageLoaded: true, loading: false, error: false });
        });
      }
    };

    img.onerror = () => {
      console.error('Error loading pattern image:', patternUrl);
      if (cacheEntry) {
        cacheEntry.loading = false;
        cacheEntry.error = true;

        // Notify all subscribers of error
        cacheEntry.subscribers.forEach((subscriber) => {
          subscriber({ imageLoaded: false, loading: false, error: true });
        });
      }
    };

    img.src = patternUrl;

    // Cleanup function
    return () => {
      cacheEntry?.subscribers.delete(updateState);
    };
  }, [gift?.symbol?.name, gift?.base_name]);

  return {
    patternSrc,
    patternLoading,
    patternLoaded,
  };
}

export function TgsOrImageGift({
  isImage,
  gift,
  className = '',
  style = { width: '200px', height: '200px' },
}: TgsOrImageGiftProps) {
  const {
    modelSrc,
    modelState,
    modelLoading,
    modelImageLoaded,
    showSkeleton,
    handleModelError,
    handleModelLoad,
  } = useGiftAssets(gift, isImage);

  const { patternSrc, patternLoading, patternLoaded } =
    useSharedPatternImage(gift);

  const patternArray = generateGiftPatternArray();
  const backgroundStyle = generateGiftBackground(gift);
  const symbolColorFilter = generateGiftColorFilter(gift);

  if (!gift) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}
        style={style}
      >
        <div className="text-center p-4">
          <div className="text-gray-500 text-sm">⚠️</div>
          <div className="text-xs text-gray-600">Gift not available</div>
        </div>
      </div>
    );
  }

  // Check if all assets are ready
  const allAssetsReady = !modelLoading && !patternLoading;

  // Show skeleton while assets are loading OR if we don't have valid sources
  if (showSkeleton || !allAssetsReady || !modelSrc) {
    return (
      <div
        className={`relative overflow-hidden rounded-lg ${className}`}
        style={{ ...style, ...backgroundStyle }}
      >
        <TgsSkeleton
          className="w-full h-full"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
          }}
        />
      </div>
    );
  }

  // Render pattern component based on state - optimized to use single shared PNG
  const renderPattern = (pattern: PatternPosition, index: number) => (
    <div
      key={index}
      className="absolute"
      style={{
        top: `${pattern.top}%`,
        left: `${pattern.left}%`,
        width: `${pattern.size.width}%`,
        height: `${pattern.size.height}%`,
        opacity: pattern.opacity,
        filter: symbolColorFilter,
        transform: 'translate(-50%, -50%)',
      }}
    >
      {patternLoaded && patternSrc && (
        <Image
          src={patternSrc}
          alt="Pattern"
          fill
          className="object-contain"
          priority={index < 5} // Only prioritize first few patterns
          loading={index < 5 ? 'eager' : 'lazy'}
          sizes="(max-width: 768px) 100vw, 50vw"
          style={{ display: 'block' }}
        />
      )}
    </div>
  );

  // Render model component based on state
  const renderModel = () => (
    <>
      {modelState === AssetFallbackState.TGS && modelSrc ? (
        <TgsViewer
          tgsUrl={modelSrc}
          style={{ width: '100%', height: '100%' }}
          className="z-10 group-hover:scale-105 transition-transform duration-200"
          onError={handleModelError}
          onLoad={handleModelLoad}
        />
      ) : modelState === AssetFallbackState.PNG && modelSrc ? (
        <Image
          src={modelSrc}
          alt="Gift Model"
          fill
          className="object-contain z-10 group-hover:scale-105 transition-transform duration-200"
          onError={() => {
            handleModelError();
          }}
          onLoad={handleModelLoad}
          priority={true}
          loading="eager"
          sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"
          style={{
            display: 'block',
            opacity: modelImageLoaded ? 1 : 0,
            transition: 'opacity 0.2s ease-in-out',
          }}
        />
      ) : null}
    </>
  );

  // Error state component
  const renderErrorState = () => (
    <div className="absolute inset-0 flex items-center justify-center">
      <div className="text-center p-4">
        <div className="text-white text-sm">⚠️</div>
        <div className="text-xs text-white opacity-80">
          Gift assets not available
        </div>
      </div>
    </div>
  );

  const hasError = modelState === AssetFallbackState.FAILED;

  return (
    <div
      className={`group relative overflow-hidden rounded-lg ${className}`}
      style={{ ...style, ...backgroundStyle }}
    >
      {/* Pattern Container */}
      <div
        className="absolute inset-0 transition-opacity duration-300"
        style={{ opacity: allAssetsReady ? 1 : 0 }}
      >
        {patternArray.map(renderPattern)}
      </div>

      {/* Model Container */}
      <div
        className="absolute inset-0 flex items-center justify-center transition-opacity duration-300"
        style={{ opacity: allAssetsReady ? 1 : 0 }}
      >
        {modelState !== AssetFallbackState.FAILED && renderModel()}
      </div>

      {/* Error State */}
      {hasError && renderErrorState()}
    </div>
  );
}
