import { HttpsError } from "firebase-functions/v2/https";

export function throwPermissionDenied(): never {
  throw new HttpsError(
    "permission-denied",
    "Only admin users can perform this operation."
  );
}

export function throwCollectionNotFound(): never {
  throw new HttpsError("not-found", "Collection not found.");
}

export function throwInvalidCollectionId(): never {
  throw new HttpsError("invalid-argument", "Collection ID is required.");
}

export function throwAdminCollectionInternalError(message?: string): never {
  throw new HttpsError(
    "internal",
    message ?? "Server error while processing admin collection operation."
  );
}
