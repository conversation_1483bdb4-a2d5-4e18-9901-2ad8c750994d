import { createServiceLogger } from "../logger/base-logger";
import { LogOperations } from "../constants";

// Create service logger using functional composition
const telegramAuthLogger = createServiceLogger("telegram-auth-function");

export const TelegramAuthLogger = {
  logAuthStarted(): void {
    telegramAuthLogger.logInfo(
      "Starting Telegram authentication",
      LogOperations.TELEGRAM_AUTH,
      {}
    );
  },

  logValidationResult(data: { isValid: boolean; requestData: any }) {
    telegramAuthLogger.logInfo(
      "Telegram data validation result",
      LogOperations.TELEGRAM_AUTH,
      {
        isValid: data.isValid,
        requestData: data.requestData,
      }
    );
  },

  logUserCreated(data: { userId: string; telegramId: string }) {
    telegramAuthLogger.logInfo(
      `New Telegram user created: ${data.userId}`,
      LogOperations.TELEGRAM_AUTH,
      {
        userId: data.userId,
        action: "user_created",
      }
    );
  },

  logAuthError(data: { error: unknown; requestData: any }) {
    telegramAuthLogger.logError(
      "Telegram authentication error",
      data.error,
      LogOperations.TELEGRAM_AUTH,
      {
        requestData: data.requestData,
      }
    );
  },

  logDevelopmentMode() {
    telegramAuthLogger.logInfo(
      "Using development mode with mock data",
      LogOperations.TELEGRAM_AUTH,
      { mode: "development" }
    );
  },

  logMockUserParsed(data: {
    telegramId: string;
    username?: string;
    firstName?: string;
  }) {
    telegramAuthLogger.logInfo(
      "Mock user data parsed",
      LogOperations.TELEGRAM_AUTH,
      data
    );
  },

  logValidationStarted() {
    telegramAuthLogger.logInfo(
      "Validating Telegram data",
      LogOperations.TELEGRAM_AUTH,
      {
        mode: "production",
      }
    );
  },

  logFirebaseUserFound(data: { userId: string }) {
    telegramAuthLogger.logInfo(
      "Found existing Firebase Auth user",
      LogOperations.FIREBASE_AUTH,
      data
    );
  },

  logFirebaseUserCreating(data: { userId: string }) {
    telegramAuthLogger.logInfo(
      "Creating new Firebase Auth user",
      LogOperations.FIREBASE_AUTH,
      data
    );
  },

  logFirebaseUserCreated(data: { userId: string }) {
    telegramAuthLogger.logInfo(
      "Firebase Auth user created successfully",
      LogOperations.FIREBASE_AUTH,
      data
    );
  },

  logCustomClaimsSet(data: { userId: string }) {
    telegramAuthLogger.logInfo(
      "Custom claims set successfully",
      LogOperations.FIREBASE_AUTH,
      data
    );
  },

  logAppConfigLoaded(data: { config: any }) {
    telegramAuthLogger.logInfo(
      "App config loaded",
      LogOperations.FIREBASE_AUTH,
      data
    );
  },

  logCustomTokenCreated(data: { userId: string }) {
    telegramAuthLogger.logInfo(
      "Custom token created successfully",
      LogOperations.FIREBASE_AUTH,
      data
    );
  },
  logFirebaseAuthError(data: { error: unknown; userId: string }) {
    telegramAuthLogger.logError(
      "Firebase Auth user creation error",
      data.error,
      LogOperations.FIREBASE_AUTH,
      { userId: data.userId }
    );
  },
};
