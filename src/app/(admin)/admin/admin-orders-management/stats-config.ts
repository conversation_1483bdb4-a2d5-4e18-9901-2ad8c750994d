import {
  getAdminOrderCountByStatus,
  getAdminOrdersCount,
  getNonAdminOrderCountByStatus,
  getNonAdminOrdersCount,
  getTotalOrdersCount,
} from '@/api/admin-api';
import { OrderStatus } from '@/mikerudenko/marketplace-shared';

import type { StatConfig, StatType } from './types';

export const statsConfig: Record<StatType, StatConfig> = {
  totalOrders: {
    apiCall: getTotalOrdersCount,
    updateStats: (stats) => ({ ...stats, totalOrders: 0 }), // Value will be set by the hook
  },
  nonAdminOrders: {
    apiCall: getNonAdminOrdersCount,
    updateStats: (stats) => stats, // Calculated value, no direct update needed
  },
  adminOrders: {
    apiCall: getAdminOrdersCount,
    updateStats: (stats) => stats, // Calculated value, no direct update needed
  },
  nonAdminActive: {
    apiCall: () => getNonAdminOrderCountByStatus(OrderStatus.ACTIVE),
    updateStats: (stats) => ({
      ...stats,
      nonAdminOrders: { ...stats.nonAdminOrders, active: 0 },
    }),
  },
  nonAdminPaid: {
    apiCall: () => getNonAdminOrderCountByStatus(OrderStatus.PAID),
    updateStats: (stats) => ({
      ...stats,
      nonAdminOrders: { ...stats.nonAdminOrders, paid: 0 },
    }),
  },
  nonAdminGiftSent: {
    apiCall: () =>
      getNonAdminOrderCountByStatus(OrderStatus.GIFT_SENT_TO_RELAYER),
    updateStats: (stats) => ({
      ...stats,
      nonAdminOrders: { ...stats.nonAdminOrders, giftSentToRelayer: 0 },
    }),
  },
  nonAdminCancelled: {
    apiCall: () => getNonAdminOrderCountByStatus(OrderStatus.CANCELLED),
    updateStats: (stats) => ({
      ...stats,
      nonAdminOrders: { ...stats.nonAdminOrders, cancelled: 0 },
    }),
  },
  nonAdminFulfilled: {
    apiCall: () => getNonAdminOrderCountByStatus(OrderStatus.FULFILLED),
    updateStats: (stats) => ({
      ...stats,
      nonAdminOrders: { ...stats.nonAdminOrders, fulfilled: 0 },
    }),
  },
  adminActive: {
    apiCall: () => getAdminOrderCountByStatus(OrderStatus.ACTIVE),
    updateStats: (stats) => ({
      ...stats,
      adminOrders: { ...stats.adminOrders, active: 0 },
    }),
  },
  adminPaid: {
    apiCall: () => getAdminOrderCountByStatus(OrderStatus.PAID),
    updateStats: (stats) => ({
      ...stats,
      adminOrders: { ...stats.adminOrders, paid: 0 },
    }),
  },
  adminGiftSent: {
    apiCall: () => getAdminOrderCountByStatus(OrderStatus.GIFT_SENT_TO_RELAYER),
    updateStats: (stats) => ({
      ...stats,
      adminOrders: { ...stats.adminOrders, giftSentToRelayer: 0 },
    }),
  },
  adminCancelled: {
    apiCall: () => getAdminOrderCountByStatus(OrderStatus.CANCELLED),
    updateStats: (stats) => ({
      ...stats,
      adminOrders: { ...stats.adminOrders, cancelled: 0 },
    }),
  },
  adminFulfilled: {
    apiCall: () => getAdminOrderCountByStatus(OrderStatus.FULFILLED),
    updateStats: (stats) => ({
      ...stats,
      adminOrders: { ...stats.adminOrders, fulfilled: 0 },
    }),
  },
};
