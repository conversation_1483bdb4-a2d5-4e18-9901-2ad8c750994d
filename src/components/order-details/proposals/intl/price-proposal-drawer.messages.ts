import { defineMessages } from 'react-intl';

export const priceProposalDrawerMessages = defineMessages({
  title: {
    id: 'priceProposalDrawer.title',
    defaultMessage: 'Propose Price',
  },
  currentPrice: {
    id: 'priceProposalDrawer.currentPrice',
    defaultMessage: 'Current Price',
  },
  proposedPrice: {
    id: 'priceProposalDrawer.proposedPrice',
    defaultMessage: 'Your Proposed Price',
  },
  savings: {
    id: 'priceProposalDrawer.savings',
    defaultMessage: 'Save {amount} TON ({percentage}%)',
  },
  info: {
    id: 'priceProposalDrawer.info',
    defaultMessage:
      'Your proposed price must be lower than the current price. The full amount will be locked as collateral until the proposal is resolved.',
  },
  submitProposal: {
    id: 'priceProposalDrawer.submitProposal',
    defaultMessage: 'Submit Proposal',
  },
  submitting: {
    id: 'priceProposalDrawer.submitting',
    defaultMessage: 'Submitting...',
  },
  cancel: {
    id: 'priceProposalDrawer.cancel',
    defaultMessage: 'Cancel',
  },
});
