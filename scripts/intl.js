const fs = require("fs");
const path = require("path");

// Define constants directly (since we can't import TS files in Node.js)
const APP_NAME = "PREM";
const PREM_CHANNEL = `@${APP_NAME.toLowerCase()}_channel`;
const PREM_SUPPORT_OFFICIAL = `@${APP_NAME.toLowerCase()}_support_official`;

// Output directories for different message types
const UI_OUT_DIR = "src/translations";

function findMessageFiles(dir, extensions = [".ts"]) {
  let results = [];

  if (!fs.existsSync(dir)) {
    return results;
  }

  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      results = results.concat(findMessageFiles(filePath, extensions));
    } else if (file.endsWith(".messages.ts")) {
      results.push(filePath);
    }
  }

  return results;
}

function extractMessagesFromFile(filePath) {
  const contents = fs.readFileSync(filePath, "utf8");
  const results = [];

  // Look for defineMessages calls
  const defineMessagesPattern = /defineMessages\(\{([\s\S]*?)\}\)/g;
  let defineMatch;

  while ((defineMatch = defineMessagesPattern.exec(contents)) !== null) {
    const messagesBlock = defineMatch[1];

    // Split by message key pattern and process each message
    const messagePattern = /(\w+):\s*\{[\s\S]*?\},?(?=\s*(?:\w+:|\/\/|$))/g;
    let match;

    while ((match = messagePattern.exec(messagesBlock)) !== null) {
      const messageBlock = match[0];

      // Extract id
      const idMatch = messageBlock.match(/id:\s*["']([^"']+)["']/);
      if (!idMatch) continue;

      // Extract defaultMessage with better handling
      let defaultMessage = "";

      // Try template literal first (backticks)
      const templateLiteralMatch = messageBlock.match(
        /defaultMessage:\s*`([\s\S]*?)`(?:\s*,|\s*\})/
      );
      if (templateLiteralMatch) {
        defaultMessage = templateLiteralMatch[1];
        // Resolve template literals with constants
        defaultMessage = defaultMessage
          .replace(/\$\{PREM_CHANNEL\}/g, PREM_CHANNEL)
          .replace(/\$\{PREM_SUPPORT_OFFICIAL\}/g, PREM_SUPPORT_OFFICIAL)
          .replace(
            /\$\{PREM_SUPPORT_OFFICIAL\.substring\(1\)\}/g,
            PREM_SUPPORT_OFFICIAL.substring(1)
          );
      } else {
        // Try quoted string (single or double quotes)
        const quotedMatch = messageBlock.match(
          /defaultMessage:\s*["']([\s\S]*?)["'](?:\s*,|\s*\})/
        );
        if (quotedMatch) {
          defaultMessage = quotedMatch[1];
        } else {
          // Try multiline quoted string
          const multilineMatch = messageBlock.match(
            /defaultMessage:\s*\n\s*["']([\s\S]*?)["'](?:\s*,|\s*\})/
          );
          if (multilineMatch) {
            defaultMessage = multilineMatch[1];
          }
        }
      }

      if (idMatch[1] && defaultMessage) {
        results.push({
          id: idMatch[1],
          defaultMessage: defaultMessage
            .replace(/\\n/g, "\n")
            .replace(/\\'/g, "'")
            .replace(/\\"/g, '"')
            .trim(),
        });
      }
    }
  }

  return results;
}

function extractAllMessages() {
  const allMessages = [];

  // Find all .messages.ts files in src directory
  const messageFiles = findMessageFiles("src");

  console.log(`Found ${messageFiles.length} message files:`);
  messageFiles.forEach((file) => console.log(`  - ${file}`));

  for (const filePath of messageFiles) {
    try {
      const messages = extractMessagesFromFile(filePath);
      allMessages.push(...messages);
      console.log(`Extracted ${messages.length} messages from ${filePath}`);
    } catch (error) {
      console.warn(
        `Failed to extract messages from ${filePath}:`,
        error.message
      );
    }
  }

  return allMessages;
}

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

function generateLocaleFiles(messages, outputDir) {
  ensureDirectoryExists(outputDir);

  // Create English locale file (base)
  const englishMessages = {};
  messages.forEach((message) => {
    englishMessages[message.id] = message.defaultMessage;
  });

  // Read existing locale files to preserve translations
  const existingEn = readExistingLocale(path.join(outputDir, "en.json"));
  const existingRu = readExistingLocale(path.join(outputDir, "ru.json"));
  const existingUa = readExistingLocale(path.join(outputDir, "ua.json"));

  // Merge existing English messages with new bot messages
  const mergedEnglish = { ...existingEn, ...englishMessages };

  // Sort keys alphabetically
  const sortedMergedEnglish = Object.keys(mergedEnglish)
    .sort()
    .reduce((obj, key) => {
      obj[key] = mergedEnglish[key];
      return obj;
    }, {});

  // Create updated locale files
  const ruMessages = {};
  const uaMessages = {};

  Object.keys(sortedMergedEnglish).forEach((key) => {
    ruMessages[key] = existingRu[key] || ""; // Keep existing translation or empty string
    uaMessages[key] = existingUa[key] || ""; // Keep existing translation or empty string
  });

  // Write all locale files
  fs.writeFileSync(
    path.join(outputDir, "en.json"),
    JSON.stringify(sortedMergedEnglish, null, 2)
  );

  fs.writeFileSync(
    path.join(outputDir, "ru.json"),
    JSON.stringify(ruMessages, null, 2)
  );

  fs.writeFileSync(
    path.join(outputDir, "ua.json"),
    JSON.stringify(uaMessages, null, 2)
  );

  console.log(
    `Generated ${
      Object.keys(sortedMergedEnglish).length
    } messages in ${outputDir}/en.json`
  );
  console.log(`Updated locale files for ru.json and ua.json in ${outputDir}`);
}

function readExistingLocale(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      return JSON.parse(fs.readFileSync(filePath, "utf8"));
    }
  } catch (error) {
    console.warn(
      `Failed to read existing locale file ${filePath}:`,
      error.message
    );
  }
  return {};
}

// Run the extraction
try {
  const allMessages = extractAllMessages();

  if (allMessages.length === 0) {
    console.warn("No messages found in any .messages.ts files");
    process.exit(1);
  }

  // Generate locale files for UI components
  generateLocaleFiles(allMessages, UI_OUT_DIR);

  console.log(`\nMessage extraction completed successfully!`);
  console.log(`Total messages extracted: ${allMessages.length}`);
} catch (error) {
  console.error("Error during message extraction:", error.message);
  process.exit(1);
}
