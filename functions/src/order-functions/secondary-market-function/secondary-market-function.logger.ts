import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const secondaryMarketLogger = createServiceLogger("secondary-market-function");

export const SecondaryMarketLogger = {
  logSetSecondaryPriceStarted({
    orderId,
    userId,
    secondaryMarketPrice,
  }: {
    orderId: string;
    userId: string;
    secondaryMarketPrice: number;
  }) {
    secondaryMarketLogger.logInfo(
      "Set secondary market price started",
      LogOperations.SECONDARY_MARKET_OPERATION,
      {
        orderId,
        userId,
        secondaryMarketPrice,
      }
    );
  },

  logSetSecondaryPriceSuccess({
    orderId,
    userId,
    secondaryMarketPrice,
  }: {
    orderId: string;
    userId: string;
    secondaryMarketPrice: number;
  }) {
    secondaryMarketLogger.logInfo(
      "Secondary market price set successfully",
      LogOperations.SET_SECONDARY_PRICE,
      {
        orderId,
        userId,
        secondaryMarketPrice,
      }
    );
  },

  logSecondaryPurchaseStarted({
    orderId,
    newBuyerId,
    oldBuyerId,
    secondaryMarketPrice,
  }: {
    orderId: string;
    newBuyerId: string;
    oldBuyerId: string;
    secondaryMarketPrice: number;
  }) {
    secondaryMarketLogger.logInfo(
      "Secondary market purchase started",
      LogOperations.SECONDARY_PURCHASE,
      {
        orderId,
        newBuyerId,
        oldBuyerId,
        secondaryMarketPrice,
      }
    );
  },

  logSecondaryPurchaseSuccess({
    orderId,
    newBuyerId,
    oldBuyerId,
    secondaryMarketPrice,
    feeAmount,
    lockedAmount,
  }: {
    orderId: string;
    newBuyerId: string;
    oldBuyerId: string;
    secondaryMarketPrice: number;
    feeAmount: number;
    lockedAmount: number;
  }) {
    secondaryMarketLogger.logInfo(
      "Secondary market purchase completed successfully",
      LogOperations.SECONDARY_PURCHASE,
      {
        orderId,
        newBuyerId,
        oldBuyerId,
        secondaryMarketPrice,
        feeAmount,
        lockedAmount,
      }
    );
  },

  logSecondaryMarketError({
    error,
    operation,
    orderId,
    userId,
  }: {
    error: unknown;
    operation: string;
    orderId?: string;
    userId?: string;
  }) {
    secondaryMarketLogger.logError(
      `Error in secondary market operation: ${operation}`,
      error,
      LogOperations.SECONDARY_MARKET_OPERATION,
      {
        orderId,
        userId,
      }
    );
  },
};
