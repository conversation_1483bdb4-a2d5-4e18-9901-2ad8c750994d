export { checkBotHealth, botHealthCheck } from "./bot-health-check-function";

export {
  logHealthCheckStarted,
  logHealthCheckCall,
  logHealthCheckResponse,
  logHealthCheckPassed,
  logHealthCheckUnhealthy,
  logHealthCheckFailed,
  logMonitorTriggered,
  logMonitorCompleted,
  logMonitorFailed,
} from "./bot-health-check-function.logger";

export { performBotHealthCheck } from "./bot-health-check-function.service";

export {
  throwBotAppUrlNotConfigured,
  throwHealthCheckFailed,
} from "./bot-health-check-function.error-handler";

// Types are now inlined in function parameters

export type { HealthCheckResult } from "./bot-health-check-function.service";
