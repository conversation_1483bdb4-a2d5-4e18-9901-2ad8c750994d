'use client';

import { cn } from '@/lib/utils';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from './table';

interface TableSkeletonProps {
  rows?: number;
  columns?: number;
  className?: string;
  showHeader?: boolean;
  headerColumns?: string[];
}

export function TableSkeleton({
  rows = 5,
  columns = 6,
  className,
  showHeader = true,
  headerColumns = [],
}: TableSkeletonProps) {
  const columnCount = headerColumns.length || columns;

  return (
    <div className={cn('rounded-md border', className)}>
      <Table>
        {showHeader && (
          <TableHeader>
            <TableRow>
              {Array.from({ length: columnCount }).map((_, index) => (
                <TableHead key={index}>
                  {headerColumns[index] ? (
                    headerColumns[index]
                  ) : (
                    <div className="h-4 bg-muted animate-pulse rounded w-16" />
                  )}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
        )}
        <TableBody>
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <TableRow key={rowIndex}>
              {Array.from({ length: columnCount }).map((_, colIndex) => (
                <TableCell key={colIndex}>
                  <div
                    className={cn(
                      'h-4 bg-muted animate-pulse rounded',
                      colIndex === 0 && 'w-8', // Checkbox column
                      colIndex === 1 && 'w-12 h-12', // Image column
                      colIndex === 2 && 'w-20', // Order number
                      colIndex === 3 && 'w-16', // Status
                      colIndex === 4 && 'w-12', // Admin type
                      colIndex === 5 && 'w-16', // Price
                      colIndex > 5 && 'w-12', // Other columns
                    )}
                  />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

interface StatsTableSkeletonProps {
  rows?: number;
  title?: string;
  className?: string;
}

export function StatsTableSkeleton({
  rows = 5,
  title,
  className,
}: StatsTableSkeletonProps) {
  return (
    <div className={cn('rounded-md border', className)}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              {title || (
                <div className="h-4 bg-muted animate-pulse rounded w-20" />
              )}
            </TableHead>
            <TableHead className="text-right">Count</TableHead>
            <TableHead className="text-right w-12">Refresh</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: rows }).map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <div className="h-4 bg-muted animate-pulse rounded w-24" />
              </TableCell>
              <TableCell className="text-right">
                <div className="h-4 bg-muted animate-pulse rounded w-8 ml-auto" />
              </TableCell>
              <TableCell className="text-right">
                <div className="h-6 w-6 bg-muted animate-pulse rounded ml-auto" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
