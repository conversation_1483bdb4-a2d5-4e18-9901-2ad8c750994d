import { log } from "../../utils/logger";
import { LogOperations } from "../../constants/bot-commands";

export const SimulationServiceLogger = {
  logMockGiftDepositSuccess(params: {
    tgId: string;
    giftId: string;
    collectionId: string;
  }) {
    log.info("Mock gift deposited successfully in simulation mode", {
      operation: LogOperations.SIMULATION_DEPOSIT_GIFT,
      tgId: params.tgId,
      giftId: params.giftId,
      collectionId: params.collectionId,
    });
  },

  logMockGiftDepositError(params: { error: unknown; tgId: string }) {
    log.error("Error depositing mock gift in simulation mode", params.error, {
      operation: LogOperations.SIMULATION_DEPOSIT_GIFT_ERROR,
      tgId: params.tgId,
    });
  },

  logGiftWithdrawalSuccess(params: {
    tgId: string;
    withdrawalGiftId: string;
    ownedGiftId: string;
  }) {
    log.info("Gift withdrawal completed successfully in simulation mode", {
      operation: LogOperations.SIMULATION_GIFT_WITHDRAWAL,
      tgId: params.tgId,
      withdrawalGiftId: params.withdrawalGiftId,
      ownedGiftId: params.ownedGiftId,
    });
  },

  logGiftWithdrawalFailed(params: {
    tgId: string;
    withdrawalGiftId: string;
    error?: string;
  }) {
    log.warn("Gift withdrawal failed in simulation mode", {
      operation: LogOperations.SIMULATION_GIFT_WITHDRAWAL,
      tgId: params.tgId,
      withdrawalGiftId: params.withdrawalGiftId,
      error: params.error,
    });
  },

  logGiftWithdrawalError(params: {
    error: unknown;
    tgId: string;
    withdrawalGiftId: string;
  }) {
    log.error(
      "Error processing gift withdrawal in simulation mode",
      params.error,
      {
        operation: LogOperations.SIMULATION_GIFT_WITHDRAWAL_ERROR,
        tgId: params.tgId,
        withdrawalGiftId: params.withdrawalGiftId,
      }
    );
  },

  logClearSessionWarning(params: { tgId: string; error: string }) {
    log.warn("Failed to clear user session during simulation", {
      operation: LogOperations.SIMULATION_CLEAR_SESSION,
      tgId: params.tgId,
      error: params.error,
    });
  },
};
