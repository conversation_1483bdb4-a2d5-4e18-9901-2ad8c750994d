import { createServiceLogger } from "../logger/base-logger";
import { LogOperations } from "../constants";

// Create service logger using functional composition
const revenueLogger = createServiceLogger("revenue-function");

export function logRevenueWithdrawal({
  withdrawAmount,
  johnDowWallet,
  userId,
  transactionHash,
}: {
  withdrawAmount: number;
  johnDowWallet: string;
  userId: string;
  transactionHash?: string;
}) {
  revenueLogger.logInfo(
    `Revenue transferred: ${withdrawAmount.toFixed(
      4
    )} TON sent to John Dow wallet`,
    LogOperations.REVENUE_WITHDRAWAL,
    {
      withdrawAmount,
      johnDowWallet,
      userId,
      transactionHash,
    }
  );
}

export function logRevenueWithdrawalError({
  error,
  withdrawAmount,
  johnDowWallet,
  userId,
}: {
  error: unknown;
  withdrawAmount: number;
  johnDowWallet: string;
  userId?: string;
}) {
  revenueLogger.logError(
    "Error in withdrawRevenue function",
    error,
    LogOperations.REVENUE_WITHDRAWAL,
    {
      withdrawAmount,
      johnDowWallet,
      userId,
    }
  );
}
