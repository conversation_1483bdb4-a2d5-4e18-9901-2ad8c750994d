import type { IntlShape } from 'react-intl';

import { errorMessages } from '@/components/app-intl/errors.messages';

export interface ErrorResponse {
  errorKey: string;
  params?: Record<string, string | number>;
  fallbackMessage?: string;
}

/**
 * Handles internationalized error messages from cloud functions
 * @param error - The error object from Firebase functions
 * @param intl - The react-intl formatMessage function
 * @returns Localized error message
 */
export function handleInternationalizedError(
  error: unknown,
  intl: IntlShape,
): string {
  // Extract error message
  let errorMessage: string;

  if (error instanceof Error) {
    errorMessage = error.message;
  } else if (
    typeof error === 'object' &&
    error !== null &&
    'message' in error
  ) {
    errorMessage = String(error.message);
  } else {
    return intl.formatMessage(errorMessages['errors.generic.unknownError']);
  }

  // Try to parse as internationalized error response
  try {
    const errorResponse: ErrorResponse = JSON.parse(errorMessage);

    if (errorResponse.errorKey) {
      // Check if we have a message definition for this error key
      const messageDescriptor =
        errorMessages[errorResponse.errorKey as keyof typeof errorMessages];

      if (messageDescriptor) {
        return intl.formatMessage(
          messageDescriptor,
          errorResponse.params || {},
        );
      }
    }

    // If we have a fallback message, use it
    if (errorResponse.fallbackMessage) {
      return errorResponse.fallbackMessage;
    }
  } catch {
    // If parsing fails, treat as regular error message
    // This handles backward compatibility with non-internationalized errors
  }

  // Return the original error message as fallback
  return errorMessage;
}

/**
 * Convenience function for use with useIntl hook
 * @param error - The error object from Firebase functions
 * @param formatMessage - The formatMessage function from useIntl
 * @returns Localized error message
 */
export function formatServerError(
  error: unknown,
  formatMessage: IntlShape['formatMessage'],
): string {
  return handleInternationalizedError(error, { formatMessage } as IntlShape);
}
