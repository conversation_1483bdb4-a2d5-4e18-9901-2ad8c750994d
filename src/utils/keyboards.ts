import { Markup } from "telegraf";
import { T } from "../i18n";
import { botMessages } from "../intl/messages";
import { loadEnvironment } from "../config/env-loader";
import { WEB_APP_URL } from "../app.constants";
import { CallbackActions } from "../constants/bot-commands";

loadEnvironment();

export const createMainKeyboard = (ctx?: any) => {
  return Markup.keyboard([
    [
      Markup.button.text(T(ctx, botMessages.depositGift.id)),
      Markup.button.text(T(ctx, botMessages.myGifts.id)),
    ],
    [Markup.button.text(T(ctx, botMessages.contactSupport.id))],
  ]).resize();
};

export const createMarketplaceInlineKeyboard = (ctx?: any) => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp(T(ctx, botMessages.openMarketplace.id), WEB_APP_URL)],
  ]);
};

export const createSupportKeyboard = (ctx?: any) => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp(T(ctx, botMessages.openMarketplace.id), WEB_APP_URL)],
    [
      Markup.button.callback(
        T(ctx, botMessages.contactSupport.id),
        CallbackActions.CONTACT_SUPPORT
      ),
    ],
  ]);
};

export { WEB_APP_URL };
