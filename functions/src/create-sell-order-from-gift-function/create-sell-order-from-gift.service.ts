import * as admin from "firebase-admin";
import {
  AppDate,
  COLLECTION_NAME,
  CollectionEntity,
  formatDateToFirebaseTimestamp,
  OrderEntity,
  ORDERS_COLLECTION_NAME,
  OrderStatus,
} from "../mikerudenko/marketplace-shared";
import { validateGiftForLinking } from "../services/gift-validation.service";
import { getUserById } from "../services/user-lookup.service";
import { getNextCounterValue } from "../services/counter-service/counter-service";
import { getAppConfig } from "../services/fee-service/fee-service";
import {
  throwCollectionNotActive,
  throwCollectionNotFound,
  throwGiftNotOwnedByUser,
  throwInvalidPrice,
} from "./create-sell-order-from-gift.error-handler";
import { CreateSellOrderFromGiftLogger } from "./create-sell-order-from-gift.logger";

export interface CreateSellOrderFromGiftParams {
  giftId: string;
  price: number;
  userId: string;
}

export interface CreateSellOrderFromGiftResult {
  success: boolean;
  message: string;
  order?: OrderEntity;
}

export async function createSellOrderFromGiftFlow({
  giftId,
  price,
  userId,
}: CreateSellOrderFromGiftParams): Promise<CreateSellOrderFromGiftResult> {
  const db = admin.firestore();

  // 1. Validate gift (existence, status, not used in other orders)
  let gift;
  try {
    gift = await validateGiftForLinking(giftId);
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === "Gift not found") {
        throw new Error("Gift not found");
      } else if (error.message.includes("status")) {
        throw new Error("Gift must have status 'deposited'");
      } else if (error.message.includes("already linked")) {
        throw new Error("Gift is already linked to another order");
      }
    }
    throw error;
  }

  CreateSellOrderFromGiftLogger.logGiftValidation({
    giftId,
    giftOwnerId: gift.owner_tg_id,
    giftCollectionId: gift.collectionId,
  });

  // 2. Get user to validate gift ownership
  const user = await getUserById(userId);
  if (!user || user.tg_id !== gift.owner_tg_id) {
    throwGiftNotOwnedByUser();
  }

  // 3. Validate price
  if (price <= 0) {
    throwInvalidPrice();
  }

  // 4. Get and validate collection
  const collectionDoc = await db
    .collection(COLLECTION_NAME)
    .doc(gift.collectionId)
    .get();

  if (!collectionDoc.exists) {
    throwCollectionNotFound();
  }

  const collection = {
    id: collectionDoc.id,
    ...collectionDoc.data(),
  } as CollectionEntity;

  CreateSellOrderFromGiftLogger.logCollectionValidation({
    collectionId: collection.id,
    collectionStatus: collection.status,
    collectionActive: collection.active,
  });

  // 5. Validate collection is active and in correct status
  if (!collection.active) {
    throwCollectionNotActive();
  }

  // 6. Get next order number from counters collection
  const orderNumber = await getNextCounterValue();

  // 7. Get fees from app config
  const appConfig = await getAppConfig();

  // 8. Create the order
  const orderRef = db.collection(ORDERS_COLLECTION_NAME).doc();
  const now = admin.firestore.Timestamp.now() as AppDate;

  const orderEntity: OrderEntity = {
    id: orderRef.id,
    number: orderNumber,
    collectionId: gift.collectionId,
    sellerId: userId,
    price,
    status: OrderStatus.ACTIVE, // Order is immediately active since gift is attached
    giftId,
    createdAt: formatDateToFirebaseTimestamp(now),
    updatedAt: formatDateToFirebaseTimestamp(now),
    fees: {
      buyer_locked_percentage: appConfig.buyer_lock_percentage,
      seller_locked_percentage: appConfig.seller_lock_percentage,
      purchase_fee: appConfig.purchase_fee,
      referrer_fee: appConfig.referrer_fee,
      order_cancellation_fee: appConfig.cancel_order_fee,
      resell_purchase_fee: appConfig.resell_purchase_fee,
      resell_purchase_fee_for_seller: appConfig.resell_purchase_fee_for_seller,
    },
  };

  // 9. Create the order
  await orderRef.set(orderEntity);

  CreateSellOrderFromGiftLogger.logOrderCreated({
    orderId: orderRef.id,
    orderNumber,
    giftId,
    userId,
    price,
    collectionId: gift.collectionId,
  });

  return {
    success: true,
    message: "Sell order created successfully from gift.",
    order: orderEntity,
  };
}
