import type { AdminOrderStats } from '@/api/admin-api';

export type StatUpdater = (stats: AdminOrderStats) => AdminOrderStats;

export interface StatConfig {
  apiCall: () => Promise<number>;
  updateStats: StatUpdater;
}

export type StatType =
  | 'totalOrders'
  | 'nonAdminOrders'
  | 'adminOrders'
  | 'nonAdminActive'
  | 'nonAdminPaid'
  | 'nonAdminGiftSent'
  | 'nonAdminCancelled'
  | 'nonAdminFulfilled'
  | 'adminActive'
  | 'adminPaid'
  | 'adminGiftSent'
  | 'adminCancelled'
  | 'adminFulfilled';
