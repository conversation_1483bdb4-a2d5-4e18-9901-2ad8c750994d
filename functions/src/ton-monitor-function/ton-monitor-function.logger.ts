import { createServiceLogger } from "../logger/base-logger";
import { LogOperations } from "../constants";

const tonMonitorLogger = createServiceLogger("ton-monitor-function");

export const TonMonitorLogger = {
  logMonitorTriggered(data: { status: string; timestamp: string }) {
    tonMonitorLogger.logInfo(
      "TON monitor triggered",
      LogOperations.MONITOR,
      data
    );
  },

  logMonitorCompleted() {
    tonMonitorLogger.logInfo(
      "TON monitor completed",
      LogOperations.MONITOR,
      {}
    );
  },

  logMonitorFailed(data: { error: unknown; status: string }) {
    tonMonitorLogger.logError(
      "TON monitor failed",
      data.error,
      LogOperations.MONITOR,
      {
        status: data.status,
      }
    );
  },

  logTransactionProcessed(data: { transactionHash: string; status: string }) {
    tonMonitorLogger.logInfo(
      `Transaction processed: ${data.transactionHash}`,
      LogOperations.TRANSACTION,
      data
    );
  },

  logMonitorError(data: { error: unknown }) {
    tonMonitorLogger.logError(
      "Error in TON monitor",
      data.error,
      LogOperations.MONITOR,
      {}
    );
  },

  logUserLookup(data: { tonWalletAddress: string; operation: string }) {
    tonMonitorLogger.logInfo(
      "Looking for user with TON address",
      LogOperations.USER_LOOKUP,
      data
    );
  },

  logUserFound(data: {
    tonWalletAddress: string;
    userId: string;
    operation: string;
  }) {
    tonMonitorLogger.logInfo(
      "Found exact match for address",
      LogOperations.USER_LOOKUP,
      data
    );
  },

  logInvalidAddress(data: { tonWalletAddress: string; operation: string }) {
    tonMonitorLogger.logInfo(
      "Invalid address format",
      LogOperations.USER_LOOKUP,
      data
    );
  },

  logRawAddressSearch(data: {
    tonWalletAddress: string;
    rawAddress: string;
    operation: string;
  }) {
    tonMonitorLogger.logInfo(
      "No exact match found, searching by raw address part",
      LogOperations.USER_LOOKUP,
      data
    );
  },

  logUserFoundByRaw(data: {
    userTonWallet: string;
    searchedAddress: string;
    userId: string;
    operation: string;
  }) {
    tonMonitorLogger.logInfo(
      "Found user by raw address",
      LogOperations.USER_LOOKUP,
      data
    );
  },

  logUserNotFound(data: { tonWalletAddress: string; operation: string }) {
    tonMonitorLogger.logInfo(
      "No user found for address",
      LogOperations.USER_LOOKUP,
      data
    );
  },

  logTransactionFiltering(data: {
    monitor: string;
    count?: number;
    totalTransactions?: number;
    newTransactions?: number;
    significantCount?: number;
    minThreshold?: number;
  }) {
    tonMonitorLogger.logInfo(
      "Transaction filtering",
      LogOperations.TRANSACTION_FILTERING,
      data
    );
  },

  logTonApiCall(data: {
    endpoint?: string;
    limit?: number;
    network?: string;
    operation: string;
    requestBody?: any;
    count?: number;
  }) {
    tonMonitorLogger.logInfo("TON API call", LogOperations.TON_API_CALL, data);
  },

  logTonApiError(data: { error: unknown; operation: string }) {
    tonMonitorLogger.logError(
      "Error fetching TON transactions",
      data.error,
      LogOperations.TON_API_CALL,
      {}
    );
  },

  logTransactionExtraction(data: {
    sender: string;
    amount: number;
    transactionId: string;
    operation: string;
  }) {
    tonMonitorLogger.logInfo(
      "Extracting transaction info",
      LogOperations.TRANSACTION_EXTRACTION,
      data
    );
  },

  logBalanceUpdate(data: {
    userId: string;
    amount: number;
    originalAmount: number;
    operation: string;
  }) {
    tonMonitorLogger.logInfo(
      "Updated balance for user after deposit",
      LogOperations.DEPOSIT_PROCESSING,
      data
    );
  },

  logBalanceUpdateError(data: {
    error: unknown;
    userId: string;
    operation: string;
  }) {
    tonMonitorLogger.logError(
      "Error updating balance for user",
      data.error,
      LogOperations.DEPOSIT_PROCESSING,
      { userId: data.userId }
    );
  },

  logMonitorStatus(data: {
    monitor: string;
    status: string;
    count?: number;
    lastCheckedLt?: string;
    latestLt?: string;
  }) {
    tonMonitorLogger.logInfo(
      `TON monitor: ${data.status}`,
      LogOperations.MONITOR,
      data
    );
  },

  logWalletNotFound(data: {
    walletAddress: string;
    transactionId: string;
    operation: string;
  }) {
    tonMonitorLogger.logInfo(
      "No user found for wallet address",
      LogOperations.TRANSACTION_PROCESSING,
      data
    );
  },
};
