import type { IntlShape } from 'react-intl';

import type { UserTxEntity } from '../mikerudenko/marketplace-shared';
import { transactionDescriptionMessages } from '../utils/intl/transaction-description.messages';

export class TransactionDescriptionService {
  private intl: IntlShape;

  constructor(intl: IntlShape) {
    this.intl = intl;
  }

  getTranslatedDescription(transaction: UserTxEntity): string {
    // If transaction has intl key, use it for translation
    if (transaction.description_intl_key) {
      return this.translateWithIntlKey(
        transaction.description_intl_key,
        transaction.description_intl_params || {},
      );
    }

    // Fall back to legacy description field
    if (transaction.description) {
      return transaction.description;
    }

    // Fallback for transactions without any description
    return this.intl.formatMessage({
      id: 'transaction.description.unknown',
      defaultMessage: 'Transaction',
    });
  }

  private translateWithIntlKey(
    intlKey: string,
    params: Record<string, string | number>,
  ): string {
    // Find the message definition by key
    const messageKey = this.getMessageKeyFromIntlKey(intlKey);
    // @ts-expect-error: Type checking doesn't work here
    const messageDefinition = transactionDescriptionMessages[messageKey];

    if (!messageDefinition) {
      // If message definition not found, return a fallback
      return this.intl.formatMessage({
        id: 'transaction.description.unknown',
        defaultMessage: 'Transaction',
      });
    }

    // Format the message with parameters
    return this.intl.formatMessage(messageDefinition, params);
  }

  private getMessageKeyFromIntlKey(intlKey: string): string {
    const prefix = 'transaction.description.';
    if (intlKey.startsWith(prefix)) {
      return intlKey.substring(prefix.length);
    }
    return intlKey;
  }
}

export function createTransactionDescriptionService(
  intl: IntlShape,
): TransactionDescriptionService {
  return new TransactionDescriptionService(intl);
}

export function getTransactionDescription(
  transaction: UserTxEntity,
  intl: IntlShape,
): string {
  const service = new TransactionDescriptionService(intl);
  return service.getTranslatedDescription(transaction);
}
