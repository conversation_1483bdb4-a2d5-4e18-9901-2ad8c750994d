import { CDN_URL, TelegramGift } from "../app.constants";
import { GiftValidationLogger } from "./gift-validation.logger";

interface GiftAttribute {
  name: string;
  rarityPermille: number;
}

const fetchIdToNameMapping = async (): Promise<Record<
  string,
  string
> | null> => {
  try {
    const response = await fetch(`${CDN_URL}/id-to-name.json`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return (await response.json()) as Record<string, string>;
  } catch (error) {
    GiftValidationLogger.logIdToNameMappingFetchError({
      error,
    });
    return null;
  }
};

const fetchAttributeData = async (
  collectionName: string,
  attributeType: "models" | "patterns" | "backdrops"
): Promise<GiftAttribute[] | null> => {
  try {
    const response = await fetch(
      `${CDN_URL}/${attributeType}/${collectionName}/${attributeType}.json`
    );
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return (await response.json()) as GiftAttribute[];
  } catch (error) {
    GiftValidationLogger.logAttributeDataFetchError({
      error,
      attributeType,
      collectionName,
    });
    return null;
  }
};

const validateAttribute = (
  giftAttributeName: string,
  giftRarityPermille: number,
  validAttributes: GiftAttribute[],
  attributeType: string
): boolean => {
  const matchingAttribute = validAttributes.find(
    (attr) =>
      attr.name === giftAttributeName &&
      attr.rarityPermille === giftRarityPermille
  );

  if (!matchingAttribute) {
    GiftValidationLogger.logInvalidAttribute({
      attributeType,
      giftAttributeName,
      giftRarityPermille,
    });
    return false;
  }

  return true;
};

/**
 * Validates gift for CREATED orders - checks if gift base name matches collection name
 * If they match, it means user is trying to trick us with wrong gift
 */
export const validateGiftForCreatedOrder = async (
  orderCollectionId: string,
  uniqueGift: TelegramGift
) => {
  try {
    // Step 1: Get collection name by orderCollectionId
    const idToNameMapping = await fetchIdToNameMapping();

    if (!idToNameMapping) {
      GiftValidationLogger.logCdnMappingFetchFailed({
        component: "validate_gift_for_created_order",
        orderCollectionId,
      });
      return { isValid: true, shouldReject: false }; // Skip validation if CDN request failed
    }

    const collectionName = idToNameMapping[orderCollectionId];

    if (!collectionName) {
      GiftValidationLogger.logCollectionNotFound({
        component: "validate_gift_for_created_order",
        orderCollectionId,
      });
      return {
        isValid: false,
        shouldReject: true,
        message: "Collection not found",
      };
    }

    // Step 2: Check if gift base name matches collection name
    const giftBaseName = uniqueGift.gift.model.name;

    GiftValidationLogger.logGiftValidationStarted({
      component: "validate_gift_for_created_order",
      orderCollectionId,
      collectionName,
      giftBaseName,
    });

    if (giftBaseName === collectionName) {
      GiftValidationLogger.logGiftNameMatchesCollection({
        component: "validate_gift_for_created_order",
        orderCollectionId,
        collectionName,
        giftBaseName,
      });
      return {
        isValid: false,
        shouldReject: true,
        message: "Don't trick us, your wrong gift will not be resent back",
      };
    }

    // If gift base name doesn't match collection name, it's valid for CREATED order
    GiftValidationLogger.logGiftValidationSuccess({
      component: "validate_gift_for_created_order",
      orderCollectionId,
      collectionName,
      giftBaseName,
    });
    return { isValid: true, shouldReject: false };
  } catch (error) {
    GiftValidationLogger.logGiftValidationError({
      component: "validate_gift_for_created_order",
      orderCollectionId,
      error,
    });
    return { isValid: false, shouldReject: true, message: "Validation error" };
  }
};

export const validateSentGiftWithOrder = async (
  orderCollectionId: string,
  uniqueGift: TelegramGift
) => {
  try {
    // Step 1: Get collection name by orderCollectionId
    const idToNameMapping = await fetchIdToNameMapping();

    if (!idToNameMapping) {
      GiftValidationLogger.logCollectionMappingFetchFailed({
        orderCollectionId,
      });
      return true; // Skip validation if CDN request failed
    }

    const collectionName = idToNameMapping[orderCollectionId];

    if (!collectionName) {
      GiftValidationLogger.logCollectionNameNotFound({
        orderCollectionId,
      });
      throw new Error("Invalid gift send");
    }

    // Step 2: Validate model
    const modelsData = await fetchAttributeData(collectionName, "models");
    if (!modelsData) {
      GiftValidationLogger.logModelsDataFetchFailed({
        collectionName,
      });
      return true; // Skip validation if CDN request failed
    }

    const isModelValid = validateAttribute(
      uniqueGift.gift.model.name,
      uniqueGift.gift.model.rarity_per_mille,
      modelsData,
      "model"
    );

    if (!isModelValid) {
      throw new Error("Invalid gift send");
    }

    // Step 3: Validate symbol (patterns)
    const patternsData = await fetchAttributeData(collectionName, "patterns");
    if (!patternsData) {
      GiftValidationLogger.logPatternsDataFetchFailed({
        collectionName,
      });
      return true; // Skip validation if CDN request failed
    }

    const isSymbolValid = validateAttribute(
      uniqueGift.gift.symbol.name,
      uniqueGift.gift.symbol.rarity_per_mille,
      patternsData,
      "symbol"
    );

    if (!isSymbolValid) {
      throw new Error("Invalid gift send");
    }

    // Step 4: Validate backdrops
    const backdropsData = await fetchAttributeData(collectionName, "backdrops");
    if (!backdropsData) {
      GiftValidationLogger.logBackdropsDataFetchFailed({
        collectionName,
      });
      return true; // Skip validation if CDN request failed
    }

    const isBackdropValid = validateAttribute(
      uniqueGift.gift.backdrop.name,
      uniqueGift.gift.backdrop.rarity_per_mille,
      backdropsData,
      "backdrop"
    );

    if (!isBackdropValid) {
      throw new Error("Invalid gift send");
    }

    GiftValidationLogger.logBackdropValidationSuccess({
      collectionName,
    });

    // All validations passed
    return true;
  } catch (error) {
    GiftValidationLogger.logGiftValidationFailed({
      error,
      orderCollectionId,
    });
    throw error;
  }
};
