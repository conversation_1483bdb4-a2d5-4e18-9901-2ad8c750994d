import { useIntl } from 'react-intl';

import type { CollectionEntity } from '@/mikerudenko/marketplace-shared';

import { orderDetailsHeaderSectionMessages } from './intl/order-details-header-section.messages';

interface OrderDetailsHeaderSectionProps {
  collection: CollectionEntity | null;
}

export function OrderDetailsHeaderSection({
  collection,
}: OrderDetailsHeaderSectionProps) {
  const { formatMessage: t } = useIntl();
  return (
    <div className="text-center space-y-2">
      <h1 className="text-2xl font-bold text-[#f5f5f5]">
        {collection?.name ||
          t(orderDetailsHeaderSectionMessages.unknownCollection)}
      </h1>
    </div>
  );
}
