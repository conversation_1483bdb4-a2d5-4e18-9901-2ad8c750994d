import * as admin from "firebase-admin";
import {
  GiftEntity,
  GIFTS_COLLECTION_NAME,
} from "../mikerudenko/marketplace-shared";

export async function getGiftById(giftId: string) {
  const db = admin.firestore();
  const giftDoc = await db.collection(GIFTS_COLLECTION_NAME).doc(giftId).get();

  if (!giftDoc.exists) {
    return null;
  }

  return { id: giftDoc.id, ...giftDoc.data() } as GiftEntity;
}
