'use client';

import { Globe } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AppLocale } from '@/core.constants';
import { useRootContext } from '@/root-context';

const LANGUAGE_LABELS = {
  [AppLocale.en]: 'English',
  [AppLocale.ru]: 'Русский',
  [AppLocale.ua]: 'Українська',
};

const getUserCountry = (): string => {
  if (typeof window === 'undefined') return 'unknown';

  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const locale = navigator.language || navigator.languages?.[0] || '';

    if (
      timezone.includes('Kiev') ||
      timezone.includes('Kyiv') ||
      locale.toLowerCase().includes('ua')
    ) {
      return 'UA';
    }

    if (
      timezone.includes('Moscow') ||
      timezone.includes('Europe/Moscow') ||
      locale.toLowerCase().startsWith('ru')
    ) {
      return 'RU';
    }

    if (timezone.includes('Minsk') || locale.toLowerCase().includes('by')) {
      return 'BY';
    }

    return 'OTHER';
  } catch {
    return 'OTHER';
  }
};

const getAvailableLanguages = (country: string): AppLocale[] => {
  switch (country) {
    case 'UA':
      return [AppLocale.en, AppLocale.ua];
    case 'RU':
    case 'BY':
      return [AppLocale.en, AppLocale.ru];
    default:
      return [AppLocale.en];
  }
};

export const LanguageSelector = () => {
  const { locale, setLocale } = useRootContext();
  const [userCountry] = useState(() => getUserCountry());
  const availableLanguages = getAvailableLanguages(userCountry);

  useEffect(() => {
    if (!availableLanguages.includes(locale)) {
      setLocale(availableLanguages[0]);
    }
  }, [availableLanguages, locale, setLocale]);

  const handleLanguageChange = (newLocale: AppLocale) => {
    setLocale(newLocale);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Globe className="h-4 w-4" />
          {LANGUAGE_LABELS[locale]}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {availableLanguages.map((lang) => (
          <DropdownMenuItem
            key={lang}
            onClick={() => handleLanguageChange(lang)}
            className={locale === lang ? 'bg-accent' : ''}
          >
            {LANGUAGE_LABELS[lang]}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
