'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Role } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';

import { RevenueDisplay } from './revenue-display';
import { StarsCountDisplay } from './stars-count-display';
import { TopUpUserBalance } from './top-up-user-balance';

export default function Admin() {
  const { currentUser, currentUserLoading } = useRootContext();
  const router = useRouter();

  useEffect(() => {
    if (typeof window === 'undefined') return;
    document?.documentElement?.classList.add('dark');
  }, []);

  useEffect(() => {
    if (currentUser && currentUser.role !== Role.ADMIN && !currentUserLoading) {
      router.replace('/');
    }
  }, [currentUser, router, currentUserLoading]);

  if (currentUser?.role !== Role.ADMIN) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Overview of marketplace revenue and statistics
        </p>
      </div>

      <TopUpUserBalance />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RevenueDisplay />
        <StarsCountDisplay />
      </div>
    </div>
  );
}
