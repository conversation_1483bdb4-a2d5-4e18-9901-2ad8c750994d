'use client';

import { useAdminAuth } from '@/hooks/use-admin-auth';

import { AdminOrdersManagement } from '../admin-orders-management';

export default function OrdersPage() {
  const { isAuthorized } = useAdminAuth();

  if (!isAuthorized) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Orders</h1>
        <p className="text-muted-foreground">
          Manage marketplace orders and batch operations
        </p>
      </div>

      <AdminOrdersManagement />
    </div>
  );
}
