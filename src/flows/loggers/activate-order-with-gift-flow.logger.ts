import { log } from "../../utils/logger";
import { LogOperations } from "../../constants/bot-commands";

export const ActivateOrderWithGiftFlowLogger = {
  logActivateOrderStarted(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.info("Activate order with gift flow started", {
      operation: LogOperations.ACTIVATE_ORDER_WITH_GIFT,
      ...params,
    });
  },

  logOrderNotFound(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.warn("Order not found for activation", {
      operation: LogOperations.ACTIVATE_ORDER_WITH_GIFT,
      ...params,
    });
  },

  logOrderNotCreatedStatus(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    currentStatus: string;
  }) {
    log.warn("Order is not in CREATED status for activation", {
      operation: LogOperations.ACTIVATE_ORDER_WITH_GIFT,
      ...params,
    });
  },

  logUserNotSeller(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    sellerId?: string;
  }) {
    log.warn("User is not the seller of the order", {
      operation: LogOperations.ACTIVATE_ORDER_WITH_GIFT,
      ...params,
    });
  },

  logGiftValidationFailed(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    collectionId: string;
    uniqueGift: any;
    validationMessage?: string;
  }) {
    log.warn("Gift validation failed for CREATED order activation", {
      operation: LogOperations.ACTIVATE_ORDER_WITH_GIFT,
      ...params,
    });
  },

  logGiftValidationError(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    error: unknown;
  }) {
    log.error(
      "Gift validation failed with exception for CREATED order activation",
      params.error,
      {
        operation: LogOperations.ACTIVATE_ORDER_WITH_GIFT,
        chat_id: params.chat_id,
        userId: params.userId,
        pendingOrderId: params.pendingOrderId,
      }
    );
  },

  logOrderActivationSuccess(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    collectionId: string;
  }) {
    log.info("Order activated successfully", {
      operation: LogOperations.ACTIVATE_ORDER_WITH_GIFT,
      ...params,
    });
  },

  logOrderActivationError(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    error: unknown;
  }) {
    log.error("Error activating order", params.error, {
      operation: LogOperations.ACTIVATE_ORDER_WITH_GIFT,
      chat_id: params.chat_id,
      userId: params.userId,
      pendingOrderId: params.pendingOrderId,
    });
  },
};
