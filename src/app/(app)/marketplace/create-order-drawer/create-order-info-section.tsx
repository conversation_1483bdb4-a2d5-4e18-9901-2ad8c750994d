'use client';

import { AlertTriangle } from 'lucide-react';
import { useIntl } from 'react-intl';

import { TonLogo } from '@/components/TonLogo';

import { createOrderDrawerMessages } from './intl/create-order-drawer.messages';

interface CreateOrderInfoSectionProps {
  lockPercentage: number;
  availableBalance: number;
  areLockAndCollateralHidden?: boolean;
}

export function CreateOrderInfoSection({
  lockPercentage,
  availableBalance,
  areLockAndCollateralHidden = false,
}: CreateOrderInfoSectionProps) {
  const { formatMessage: t } = useIntl();

  return (
    <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
      <div className="w-full flex items-start gap-3">
        <AlertTriangle className="w-5 h-5 text-[#6ab2f2] flex-shrink-0 mt-0.5" />
        <div className="w-full text-sm">
          <p className="font-medium text-[#f5f5f5] mb-2">
            {t(createOrderDrawerMessages.orderInformation)}
          </p>
          <div className="space-y-2">
            {!areLockAndCollateralHidden && (
              <div className="flex justify-between items-center flex-wrap py-1">
                <span className="text-[#708499]">
                  {t(createOrderDrawerMessages.lockPercentage)}
                </span>
                <span className="text-[#6ab2f2] font-semibold">
                  {(lockPercentage * 100).toFixed(0)}%
                </span>
              </div>
            )}
            <div className="flex justify-between items-center flex-wrap py-1">
              <span className="text-[#708499]">
                {t(createOrderDrawerMessages.availableBalance)}
              </span>
              <div className="flex items-center flex-wrap gap-1">
                <span className="text-[#6ab2f2] font-semibold">
                  {availableBalance.toFixed(2)}
                </span>
                <TonLogo size={24} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
