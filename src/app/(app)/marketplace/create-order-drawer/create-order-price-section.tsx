'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { useIntl } from 'react-intl';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { CollectionEntity } from '@/mikerudenko/marketplace-shared';

import { createOrderDrawerMessages } from './intl/create-order-drawer.messages';

interface CreateOrderPriceSectionProps {
  itemPrice: string;
  onPriceChange: (price: string) => void;
  selectedCollectionData: CollectionEntity | undefined;
  price: number;
}

export function CreateOrderPriceSection({
  itemPrice,
  onPriceChange,
  selectedCollectionData,
  price,
}: CreateOrderPriceSectionProps) {
  const { formatMessage: t } = useIntl();

  const showFloorPriceError =
    selectedCollectionData &&
    itemPrice &&
    price < selectedCollectionData.floorPrice;

  const placeholder = selectedCollectionData
    ? t(createOrderDrawerMessages.minimumPrice, {
        amount: selectedCollectionData.floorPrice,
      })
    : t(createOrderDrawerMessages.enterPrice);

  return (
    <div>
      <Label
        htmlFor="item-price"
        className="text-sm font-medium text-[#f5f5f5]"
      >
        {t(createOrderDrawerMessages.itemPriceLabel)}
      </Label>
      <Input
        id="item-price"
        type="number"
        step="0.01"
        placeholder={placeholder}
        value={itemPrice}
        onChange={(e) => onPriceChange(e.target.value)}
        className="mt-2 bg-[#232e3c]/50 border-[#3a4a5c]/50 text-[#f5f5f5] placeholder:text-[#708499] focus:border-[#6ab2f2] focus:ring-[#6ab2f2]/20"
        min={selectedCollectionData?.floorPrice || 0}
      />
      {showFloorPriceError && (
        <Caption level="2" weight="3" className="text-[#ec3942] mt-1">
          {t(createOrderDrawerMessages.priceFloorError, {
            amount: selectedCollectionData.floorPrice,
          })}
        </Caption>
      )}
    </div>
  );
}
