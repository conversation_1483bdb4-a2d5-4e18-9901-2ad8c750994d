'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';
import { z } from 'zod';

import { updateUser } from '@/api/user-api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useRootContext } from '@/root-context';

import { profileFormMessages } from './intl/profile-form.messages';

type ProfileFormData = {
  name: string;
};

export const ProfileForm = () => {
  const { formatMessage: t } = useIntl();
  const { currentUser } = useRootContext();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const profileFormSchema = z.object({
    name: z
      .string()
      .min(1, t(profileFormMessages.nameIsRequired))
      .max(50, t(profileFormMessages.nameTooLong)),
  });

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: currentUser?.displayName || currentUser?.name || '',
    },
  });

  const onSubmit = async (data: ProfileFormData) => {
    if (!currentUser) return;

    setIsSubmitting(true);
    try {
      await updateUser(currentUser.id, {
        displayName: data.name,
      });

      toast.success(t(profileFormMessages.profileUpdatedSuccessfully));
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error(t(profileFormMessages.failedToUpdateProfile));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!currentUser) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t(profileFormMessages.editProfile)}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t(profileFormMessages.displayName)}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t(profileFormMessages.enterYourDisplayName)}
                      {...field}
                      className="bg-[#232e3c] border-[#3a4a5c] text-white"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-[#0098EA] hover:bg-[#0088d4] text-white"
            >
              {isSubmitting
                ? t(profileFormMessages.updating)
                : t(profileFormMessages.updateProfile)}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
