import { defineMessages } from 'react-intl';

export const resellTxHistoryMessages = defineMessages({
  resellTransaction: {
    id: 'resellTxHistory.resellTransaction',
    defaultMessage: 'Resell Transaction',
  },
  executionPrice: {
    id: 'resellTxHistory.executionPrice',
    defaultMessage: 'Execution Price',
  },
  reseller: {
    id: 'resellTxHistory.reseller',
    defaultMessage: 'Reseller',
  },
  buyer: {
    id: 'resellTxHistory.buyer',
    defaultMessage: 'Buyer',
  },
  yourEarnings: {
    id: 'resellTxHistory.yourEarnings',
    defaultMessage: 'Your Earnings',
  },
  resellHistory: {
    id: 'resellTxHistory.resellHistory',
    defaultMessage: 'Resell History',
  },
  loadingResellHistory: {
    id: 'resellTxHistory.loadingResellHistory',
    defaultMessage: 'Loading resell history...',
  },
  close: {
    id: 'resellTxHistory.close',
    defaultMessage: 'Close',
  },
  noResellTransactions: {
    id: 'resellTxHistory.noResellTransactions',
    defaultMessage: 'No resell transactions found',
  },
  resellHistoryCount: {
    id: 'resellTxHistory.resellHistoryCount',
    defaultMessage: 'Resell History ({count})',
  },
  showingEarnings: {
    id: 'resellTxHistory.showingEarnings',
    defaultMessage: 'Showing your earnings from each resell transaction',
  },
  failedToFetchHistory: {
    id: 'resellTxHistory.failedToFetchHistory',
    defaultMessage: 'Failed to fetch resell history',
  },
});
