import { createServiceLogger } from "./logger";
import { LogOperations } from "../constants";

// Create service logger using functional composition
const withdrawalLimitLogger = createServiceLogger("withdrawal-limit");

export const WithdrawalLimitLogger = {
  logLimitCheckCompleted({
    userId,
    requestedAmount,
    maxWithdrawalAmount,
    currentWithdrawn,
    remainingLimit,
    canWithdraw,
  }: {
    userId: string;
    requestedAmount: number;
    maxWithdrawalAmount: number;
    currentWithdrawn: number;
    remainingLimit: number;
    canWithdraw: boolean;
  }) {
    withdrawalLimitLogger.logInfo(
      "Withdrawal limit check completed",
      LogOperations.CHECK_WITHDRAWAL_LIMIT,
      {
        userId,
        requestedAmount,
        maxWithdrawalAmount,
        currentWithdrawn,
        remainingLimit,
        canWithdraw,
      }
    );
  },

  logLimitCheckError({
    error,
    userId,
    requestedAmount,
    maxWithdrawalAmount,
  }: {
    error: unknown;
    userId: string;
    requestedAmount: number;
    maxWithdrawalAmount: number;
  }) {
    withdrawalLimitLogger.logError(
      "Error checking withdrawal limit",
      error,
      LogOperations.CHECK_WITHDRAWAL_LIMIT,
      {
        userId,
        requestedAmount,
        maxWithdrawalAmount,
      }
    );
  },

  logTrackingUpdated({
    userId,
    withdrawnAmount,
    newWithdrawnAmount,
    resetTime,
  }: {
    userId: string;
    withdrawnAmount: number;
    newWithdrawnAmount: number;
    resetTime: Date;
  }) {
    withdrawalLimitLogger.logInfo(
      "Withdrawal tracking updated",
      LogOperations.UPDATE_WITHDRAWAL_TRACKING,
      {
        userId,
        withdrawnAmount,
        newWithdrawnAmount,
        resetTime,
      }
    );
  },

  logTrackingUpdateError({
    error,
    userId,
    withdrawnAmount,
  }: {
    error: unknown;
    userId: string;
    withdrawnAmount: number;
  }) {
    withdrawalLimitLogger.logError(
      "Error updating withdrawal tracking",
      error,
      LogOperations.UPDATE_WITHDRAWAL_TRACKING,
      {
        userId,
        withdrawnAmount,
      }
    );
  },
};
