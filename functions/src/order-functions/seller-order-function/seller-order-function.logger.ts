import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const sellerOrderLogger = createServiceLogger("seller-order-function");

export const SellerOrderLogger = {
  logCreateOrderError({
    error,
    operation,
    requestData,
    sellerId,
  }: {
    error: unknown;
    operation: LogOperations;
    requestData?: any;
    sellerId?: string;
  }) {
    sellerOrderLogger.logError(
      "Error creating seller order",
      error,
      operation,
      {
        sellerId,
        requestData,
      }
    );
  },

  logPurchaseError({
    error,
    operation,
    requestData,
    userId,
  }: {
    error: unknown;
    operation: LogOperations;
    requestData?: any;
    userId?: string;
  }) {
    sellerOrderLogger.logError("Error in seller purchase", error, operation, {
      requestData,
      userId,
    });
  },
};
