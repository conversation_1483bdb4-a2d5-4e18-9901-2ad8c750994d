import { Loader2 } from 'lucide-react';
import type { ReactNode } from 'react';
import { useIntl } from 'react-intl';

import { AuthWrapper } from '@/components/auth/auth-wrapper';
import { ConfirmWrapper } from '@/components/shared/confirm-wrapper';
import { <PERSON><PERSON> } from '@/components/ui/button';

import { orderDetailsActionButtonsMessages } from './intl/order-details-action-buttons.messages';

interface OrderDetailsActionButtonsProps {
  primaryAction: {
    label: ReactNode;
    onClick: () => void;
    loading: boolean;
    disabled?: boolean;
  };
  secondaryAction?: {
    label: ReactNode;
    onClick: () => void;
    disabled?: boolean;
  };
  onClose?: () => void;
  actionLoading: boolean;
  shouldShowCloseButton?: boolean;
}

export function OrderDetailsActionButtons({
  primaryAction,
  secondaryAction,
  onClose,
  actionLoading,
  shouldShowCloseButton = false,
}: OrderDetailsActionButtonsProps) {
  const { formatMessage: t } = useIntl();
  return (
    <div className="space-y-3 pt-4">
      <AuthWrapper>
        <ConfirmWrapper>
          <Button
            onClick={primaryAction.onClick}
            disabled={primaryAction.loading || primaryAction.disabled}
            className="w-full h-12 bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white border-0 rounded-2xl"
          >
            {primaryAction.loading ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin mr-2" />
                {t(orderDetailsActionButtonsMessages.processing)}
              </>
            ) : (
              primaryAction.label
            )}
          </Button>
        </ConfirmWrapper>
      </AuthWrapper>

      {secondaryAction && (
        <Button
          onClick={secondaryAction.onClick}
          disabled={secondaryAction.disabled || actionLoading}
          variant="outline"
          className="w-full h-12 border-[#6ab2f2] text-[#6ab2f2] hover:bg-[#6ab2f2]/10 bg-transparent rounded-2xl"
        >
          {secondaryAction.label}
        </Button>
      )}

      {shouldShowCloseButton && onClose && (
        <Button
          variant="outline"
          onClick={onClose}
          className="w-full h-12 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#232e3c]/50 bg-transparent rounded-2xl"
          disabled={actionLoading}
        >
          {t(orderDetailsActionButtonsMessages.close)}
        </Button>
      )}
    </div>
  );
}
