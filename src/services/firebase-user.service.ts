import { getFirestore } from "../firebase/firebase-admin";

export interface UserData {
  tg_id: string;
  userLanguage?: string;
}

export class FirebaseUserService {
  private static db = getFirestore();

  static async getUserById(userId: string): Promise<UserData> {
    const userDoc = await this.db.collection("users").doc(userId).get();

    if (!userDoc.exists) {
      throw new Error(`User ${userId} not found`);
    }

    const userData = userDoc.data() as UserData;

    if (!userData.tg_id) {
      throw new Error(`User ${userId} does not have a Telegram ID`);
    }

    return userData;
  }
}
