import { defineMessages } from 'react-intl';

export const orderStatusMessages = defineMessages({
  created: {
    id: 'orderStatus.created',
    defaultMessage: 'Created',
  },
  active: {
    id: 'orderStatus.active',
    defaultMessage: 'Active',
  },
  paid: {
    id: 'orderStatus.paid',
    defaultMessage: 'Paid',
  },
  giftSentToRelayer: {
    id: 'orderStatus.giftSentToRelayer',
    defaultMessage: 'Sent to <PERSON><PERSON>',
  },
  fulfilled: {
    id: 'orderStatus.fulfilled',
    defaultMessage: 'Fulfilled',
  },
  cancelled: {
    id: 'orderStatus.cancelled',
    defaultMessage: 'Cancelled',
  },
});
