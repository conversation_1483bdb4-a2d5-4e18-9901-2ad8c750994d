import { log } from "./utils/logger";
import { LogOperations } from "./constants/bot-commands";

export const BotLogger = {
  logBotError(params: { error: unknown; chatId?: number; userId?: number }) {
    log.error("Bot error", params.error, {
      operation: LogOperations.BOT_ERROR,
      ...(params.chatId !== undefined && { chatId: params.chatId }),
      ...(params.userId !== undefined && { userId: params.userId }),
    });
  },
};
