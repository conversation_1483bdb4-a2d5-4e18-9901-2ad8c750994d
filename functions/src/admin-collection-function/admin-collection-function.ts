import { HttpsError, onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig, LogOperations } from "../constants";
import {
  validateAdminUser,
  recalculateOrderDeadlines,
  clearOrderDeadlines,
} from "./admin-collection-function.service";
import {
  logAdminCollectionOperation,
  logAdminCollectionError,
} from "./admin-collection-function.logger";
import { throwAdminCollectionInternalError } from "./admin-collection-function.error-handler";

export const recalculateDeadlines = onCall<{
  collectionId: string;
}>(commonFunctionsConfig, async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  const { collectionId } = request.data;
  const userId = request.auth.uid;

  try {
    await validateAdminUser(userId);
    const updatedCount = await recalculateOrderDeadlines(collectionId);

    logAdminCollectionOperation({
      operation: LogOperations.RECALCULATE_DEADLINES,
      collectionId,
      updatedCount,
      userId,
    });

    return {
      success: true,
      message: `Successfully recalculated deadlines for ${updatedCount} orders`,
      updatedCount,
    };
  } catch (error) {
    logAdminCollectionError({
      error,
      operation: LogOperations.RECALCULATE_DEADLINES,
      collectionId,
      userId,
    });

    if (error instanceof HttpsError) {
      throw error;
    }
    throwAdminCollectionInternalError((error as any).message);
  }
});

export const clearDeadlines = onCall<{
  collectionId: string;
}>(commonFunctionsConfig, async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  const { collectionId } = request.data;
  const userId = request.auth.uid;

  try {
    await validateAdminUser(userId);
    const updatedCount = await clearOrderDeadlines(collectionId);

    logAdminCollectionOperation({
      operation: LogOperations.CLEAR_DEADLINES,
      collectionId,
      updatedCount,
      userId,
    });

    return {
      success: true,
      message: `Successfully cleared deadlines for ${updatedCount} orders`,
      updatedCount,
    };
  } catch (error) {
    logAdminCollectionError({
      error,
      operation: LogOperations.CLEAR_DEADLINES,
      collectionId,
      userId,
    });

    if (error instanceof HttpsError) {
      throw error;
    }
    throwAdminCollectionInternalError((error as any).message);
  }
});
