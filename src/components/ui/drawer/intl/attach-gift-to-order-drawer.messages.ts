import { defineMessages } from 'react-intl';

export const attachGiftToOrderDrawerMessages = defineMessages({
  title: {
    id: 'attachGiftToOrderDrawer.title',
    defaultMessage: 'Attach Gift to Order',
  },
  instructionsTitle: {
    id: 'attachGiftToOrderDrawer.instructionsTitle',
    defaultMessage: 'How to activate your order:',
  },
  instructionStep1: {
    id: 'attachGiftToOrderDrawer.instructionStep1',
    defaultMessage:
      'Send the gift directly to the relayer, then return to the app and attach the corresponding gift to your order',
  },

  selectGift: {
    id: 'attachGiftToOrderDrawer.selectGift',
    defaultMessage: 'Select a gift to attach',
  },
  noGiftsAvailable: {
    id: 'attachGiftToOrderDrawer.noGiftsAvailable',
    defaultMessage: 'No gifts available for this collection',
  },
  linkGiftToOrder: {
    id: 'attachGiftToOrderDrawer.linkGiftToOrder',
    defaultMessage: 'Link Gift to Order',
  },
  linking: {
    id: 'attachGiftToOrderDrawer.linking',
    defaultMessage: 'Linking...',
  },
  giftLinkedSuccessfully: {
    id: 'attachGiftToOrderDrawer.giftLinkedSuccessfully',
    defaultMessage: 'Gift linked to order successfully',
  },
  errorLoadingGifts: {
    id: 'attachGiftToOrderDrawer.errorLoadingGifts',
    defaultMessage: 'Error loading available gifts',
  },
  errorLinkingGift: {
    id: 'attachGiftToOrderDrawer.errorLinkingGift',
    defaultMessage: 'Error linking gift to order',
  },

  // Activate Order Messages (when no gifts available)
  activateOrderTitle: {
    id: 'attachGiftToOrderDrawer.activateOrderTitle',
    defaultMessage: 'How to activate your order:',
  },
  activateStep1: {
    id: 'attachGiftToOrderDrawer.activateStep1',
    defaultMessage: 'Deposit gift to bot',
  },
  activateStep2: {
    id: 'attachGiftToOrderDrawer.activateStep2',
    defaultMessage: 'Go to the bot',
  },
  activateStep3: {
    id: 'attachGiftToOrderDrawer.activateStep3',
    defaultMessage: 'Press "My Sell Orders" waiting activation',
  },
  activateStep4: {
    id: 'attachGiftToOrderDrawer.activateStep4',
    defaultMessage: 'Select order to activate',
  },
  activateStep5: {
    id: 'attachGiftToOrderDrawer.activateStep5',
    defaultMessage: 'Send gift to relayer to activate',
  },
});
