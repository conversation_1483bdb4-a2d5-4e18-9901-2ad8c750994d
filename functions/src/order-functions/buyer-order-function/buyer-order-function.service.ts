import * as admin from "firebase-admin";
import { UserType } from "../../mikerudenko/marketplace-shared";
import {
  requireAuthentication,
  validateBuyerOwnership,
  validateOrderCreationParams,
  validatePurchaseParams,
  getUserData,
} from "../../services/auth-middleware";
import { createOrder } from "../../services/order-creation-service";
import { processPurchase } from "../../services/purchase-flow-service";
import { validateTelegramIdForOrderOperation } from "../../services/telegram-validation-service";
import { refundAllActiveProposals } from "../../proposal-functions/proposal-service";

export interface CreateOrderAsBuyerParams {
  buyerId: string;
  collectionId: string;
  price: number;
}

export interface MakePurchaseAsBuyerParams {
  buyerId: string;
  orderId: string;
}

export async function validateCreateOrderRequest(
  request: any,
  params: CreateOrderAsBuyerParams
) {
  const authRequest = requireAuthentication(request);
  validateOrderCreationParams(params, UserType.BUYER);
  validateBuyerOwnership(authRequest, params.buyerId);

  const user = await getUserData(authRequest.auth.uid);
  validateTelegramIdForOrderOperation(user, "create orders");

  return { authRequest, user };
}

export async function validatePurchaseRequest(
  request: any,
  params: MakePurchaseAsBuyerParams
) {
  const authRequest = requireAuthentication(request);
  validatePurchaseParams(params, UserType.BUYER);
  validateBuyerOwnership(authRequest, params.buyerId);

  // Validate user has telegram ID for purchase operations
  const user = await getUserData(authRequest.auth.uid);
  validateTelegramIdForOrderOperation(user, "make purchases");

  return { authRequest, user };
}

export async function createBuyerOrder(params: CreateOrderAsBuyerParams) {
  const db = admin.firestore();
  const { buyerId, collectionId, price } = params;

  return createOrder(db, {
    userId: buyerId,
    collectionId,
    price,
    giftId: null,
    userType: UserType.BUYER,
    secondaryMarketPrice: null,
  });
}

export async function processBuyerPurchase(params: MakePurchaseAsBuyerParams) {
  const db = admin.firestore();
  const { buyerId, orderId } = params;

  // Run proposal refund and purchase processing in parallel where possible
  // Note: refundAllActiveProposals must complete before processPurchase for data consistency
  await refundAllActiveProposals(orderId);

  return processPurchase(db, {
    userId: buyerId,
    orderId,
    userType: UserType.BUYER,
  });
}
