'use client';

import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import { getOrderProposals, getUserActiveProposal } from '@/api/proposal-api';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';
import { formatProposalTime, roundToThreeDecimals } from '@/lib/utils';
import type {
  OrderEntity,
  ProposalEntity,
  UserType,
} from '@/mikerudenko/marketplace-shared';
import { ProposalStatus } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';
import {
  getProposalStatusColor,
  getProposalStatusMessageKey,
  isUserProposer,
  isUserSeller,
} from '@/services/proposal-service';

import { proposalsTableMessages } from './intl/proposals-table.messages';

interface ProposalsTableProps {
  order: OrderEntity;
  userType?: UserType;
  onAcceptProposal?: (proposalId: string) => void;
  onCancelProposal?: (proposalId: string) => void;
  acceptingProposalId?: string;
  cancellingProposal?: boolean;
  onRefreshProposals?: () => void;
}

export function ProposalsTable({
  order,
  userType,
  onAcceptProposal,
  onCancelProposal,
  acceptingProposalId,
  cancellingProposal,
  onRefreshProposals,
}: ProposalsTableProps) {
  const { formatMessage: t } = useIntl();
  const { currentUser } = useRootContext();

  const [proposals, setProposals] = useState<ProposalEntity[]>([]);
  const [userActiveProposal, setUserActiveProposal] =
    useState<ProposalEntity | null>(null);
  const [loading, setLoading] = useState(true);

  const loadProposals = async () => {
    try {
      setLoading(true);
      // Load ALL proposals at once (no lazy loading)
      const result = await getOrderProposals(order.id!);
      setProposals(result);
    } catch (error) {
      console.error('Error loading proposals:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUserActiveProposal = async () => {
    if (!currentUser?.id) return;

    try {
      const proposal = await getUserActiveProposal(order.id!, currentUser.id);
      setUserActiveProposal(proposal);
    } catch (error) {
      console.error('Error loading user active proposal:', error);
    }
  };

  useEffect(() => {
    if (order.id) {
      loadProposals();
      loadUserActiveProposal();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [order.id, currentUser?.id]);

  // Expose refresh function to parent
  useEffect(() => {
    if (onRefreshProposals) {
      onRefreshProposals();
    }
  }, [proposals, onRefreshProposals]);

  const handleAcceptProposal = (proposalId: string) => {
    if (onAcceptProposal) {
      onAcceptProposal(proposalId);
    }
  };

  const handleCancelProposal = () => {
    if (onCancelProposal && userActiveProposal?.id) {
      onCancelProposal(userActiveProposal.id);
    }
  };

  const getProposalStatusText = (status: ProposalStatus) => {
    const messageKey = getProposalStatusMessageKey(status);
    return typeof messageKey === 'string' ? messageKey : t(messageKey);
  };

  const isSeller = userType === 'seller' || isUserSeller(order, currentUser);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="w-6 h-6 animate-spin text-[#6ab2f2]" />
      </div>
    );
  }

  if (proposals.length === 0 && !userActiveProposal) {
    return (
      <div className="text-center py-8 text-gray-400">
        {t(proposalsTableMessages.noProposals)}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-white">
        {t(proposalsTableMessages.priceProposals)}
      </h3>

      {userActiveProposal && (
        <div className="bg-[#232e3c] rounded-lg p-4 border border-[#6ab2f2]/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-sm text-[#6ab2f2] font-medium">
                {t(proposalsTableMessages.yourProposal)}
              </span>
              <div className="flex items-center gap-1">
                <span className="text-white font-bold">
                  {roundToThreeDecimals(userActiveProposal.proposed_price)}
                </span>
                <TonLogo size={16} />
              </div>
              <span
                className={`text-sm ${getProposalStatusColor(userActiveProposal.status)}`}
              >
                {getProposalStatusText(userActiveProposal.status)}
              </span>
              {userActiveProposal.createdAt && (
                <span className="text-xs text-gray-400">
                  {formatProposalTime(userActiveProposal.createdAt)}
                </span>
              )}
            </div>

            {userActiveProposal.status === ProposalStatus.ACTIVE && (
              <Button
                onClick={handleCancelProposal}
                disabled={cancellingProposal}
                variant="outline"
                size="sm"
                className="border-red-500 text-red-500 hover:bg-red-500/10"
              >
                {cancellingProposal ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-1" />
                    {t(proposalsTableMessages.cancelling)}
                  </>
                ) : (
                  t(proposalsTableMessages.cancel)
                )}
              </Button>
            )}
          </div>
        </div>
      )}

      {proposals.length > 0 && (
        <div className="space-y-3">
          {proposals
            .filter((proposal) => !isUserProposer(proposal, currentUser))
            .map((proposal) => (
              <div
                key={proposal.id}
                className="bg-[#1a2332] rounded-lg p-4 border border-[#3a4a5c]"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                      <span className="text-white font-bold">
                        {roundToThreeDecimals(proposal.proposed_price)}
                      </span>
                      <TonLogo size={16} />
                    </div>
                    <span
                      className={`text-sm ${getProposalStatusColor(proposal.status)}`}
                    >
                      {getProposalStatusText(proposal.status)}
                    </span>
                    {proposal.createdAt && (
                      <span className="text-xs text-gray-400">
                        {formatProposalTime(proposal.createdAt)}
                      </span>
                    )}
                  </div>

                  {isSeller && proposal.status === ProposalStatus.ACTIVE && (
                    <Button
                      onClick={() => handleAcceptProposal(proposal.id!)}
                      disabled={!!acceptingProposalId}
                      size="sm"
                      className="bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white"
                    >
                      {acceptingProposalId === proposal.id ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin mr-1" />
                          {t(proposalsTableMessages.accepting)}
                        </>
                      ) : (
                        t(proposalsTableMessages.accept)
                      )}
                    </Button>
                  )}
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  );
}
