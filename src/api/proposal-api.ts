import {
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import { AppCloudFunctions } from '@/core.constants';
import type {
  OrderEntity,
  ProposalEntity,
  UserEntity,
} from '@/mikerudenko/marketplace-shared';
import {
  ORDERS_COLLECTION_NAME,
  OrderStatus,
  PROPOSAL_PRICES_SUBCOLLECTION,
  ProposalStatus,
} from '@/mikerudenko/marketplace-shared';
import { firebaseFunctions, firestore } from '@/root-context';

import { getCurrentUserId } from './auth-api';

export interface ProposeOrderPriceResponse {
  success: boolean;
  message: string;
  proposalId?: string;
}

export interface CancelProposalResponse {
  success: boolean;
  message: string;
  refundedAmount?: number;
}

export interface AcceptProposalResponse {
  success: boolean;
  message: string;
  newOrderPrice?: number;
}

// Validation functions
export const validateProposedPrice = (
  proposedPrice: number,
  currentPrice: number,
  minimumExistingProposalPrice?: number | null,
): { isValid: boolean; error?: string } => {
  if (proposedPrice <= 0) {
    return { isValid: false, error: 'Proposed price must be greater than 0' };
  }

  if (proposedPrice >= currentPrice) {
    return {
      isValid: false,
      error: 'Proposed price must be lower than current price',
    };
  }

  // New validation: proposed price must be lower than existing proposals
  if (
    minimumExistingProposalPrice !== null &&
    minimumExistingProposalPrice !== undefined
  ) {
    if (proposedPrice >= minimumExistingProposalPrice) {
      return {
        isValid: false,
        error: 'Proposed price must be lower than existing proposals',
      };
    }
  }

  return { isValid: true };
};

export const canUserProposeOnOrder = (
  order: OrderEntity,
  currentUser: UserEntity | null,
): { canPropose: boolean; reason?: string } => {
  if (!currentUser) {
    return { canPropose: false, reason: 'User must be authenticated' };
  }

  // Check if this is a sell order (has sellerId but no buyerId)
  if (!order.sellerId || order.buyerId) {
    return { canPropose: false, reason: 'Can only propose on sell orders' };
  }

  if (order.sellerId === currentUser.id) {
    return { canPropose: false, reason: 'Cannot propose on your own order' };
  }

  if (order.status !== OrderStatus.ACTIVE) {
    return { canPropose: false, reason: 'Can only propose on active orders' };
  }

  if (order.secondaryMarketPrice) {
    return {
      canPropose: false,
      reason: 'Cannot propose on orders with secondary market price',
    };
  }

  return { canPropose: true };
};

export const proposeOrderPrice = async (
  orderId: string,
  proposedPrice: number,
) => {
  try {
    const proposeOrderPriceFunction = httpsCallable<
      { orderId: string; proposedPrice: number },
      ProposeOrderPriceResponse
    >(firebaseFunctions, AppCloudFunctions.PROPOSE_ORDER_PRICE);

    const result = await proposeOrderPriceFunction({
      orderId,
      proposedPrice,
    });

    return result.data;
  } catch (error) {
    console.error('Error proposing order price:', error);
    throw error;
  }
};

export const cancelProposal = async (orderId: string, proposalId: string) => {
  try {
    const cancelProposalFunction = httpsCallable<
      { orderId: string; proposalId: string },
      CancelProposalResponse
    >(firebaseFunctions, AppCloudFunctions.CANCEL_PROPOSAL);

    const result = await cancelProposalFunction({
      orderId,
      proposalId,
    });

    return result.data;
  } catch (error) {
    console.error('Error cancelling proposal:', error);
    throw error;
  }
};

export const acceptProposal = async (orderId: string, proposalId: string) => {
  try {
    const acceptProposalFunction = httpsCallable<
      { orderId: string; proposalId: string },
      AcceptProposalResponse
    >(firebaseFunctions, AppCloudFunctions.ACCEPT_PROPOSAL);

    const result = await acceptProposalFunction({
      orderId,
      proposalId,
    });

    return result.data;
  } catch (error) {
    console.error('Error accepting proposal:', error);
    throw error;
  }
};

export const getOrderProposals = async (
  orderId: string,
): Promise<ProposalEntity[]> => {
  try {
    const proposalsRef = collection(
      firestore,
      ORDERS_COLLECTION_NAME,
      orderId,
      PROPOSAL_PRICES_SUBCOLLECTION,
    );

    // Load ALL proposals at once (no lazy loading)
    const q = query(
      proposalsRef,
      where('status', '==', ProposalStatus.ACTIVE),
      orderBy('proposed_price', 'asc'), // Order by price ascending to show lowest first
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as ProposalEntity[];
  } catch (error) {
    console.error('Error fetching order proposals:', error);
    return [];
  }
};

export const getMinimumProposalPrice = async (
  orderId: string,
): Promise<number | null> => {
  try {
    const proposalsRef = collection(
      firestore,
      ORDERS_COLLECTION_NAME,
      orderId,
      PROPOSAL_PRICES_SUBCOLLECTION,
    );

    const q = query(
      proposalsRef,
      where('status', '==', ProposalStatus.ACTIVE),
      orderBy('proposed_price', 'asc'),
      limit(1), // Get only the lowest price proposal
    );

    const snapshot = await getDocs(q);
    if (snapshot.empty) {
      return null; // No existing proposals
    }

    const lowestProposal = snapshot.docs[0].data() as ProposalEntity;
    return lowestProposal.proposed_price;
  } catch (error) {
    console.error('Error fetching minimum proposal price:', error);
    return null;
  }
};

export const getUserActiveProposal = async (
  orderId: string,
  userId?: string,
) => {
  try {
    const currentUserId = userId || getCurrentUserId();
    if (!currentUserId) {
      return null;
    }

    const proposalsRef = collection(
      firestore,
      ORDERS_COLLECTION_NAME,
      orderId,
      PROPOSAL_PRICES_SUBCOLLECTION,
    );

    const q = query(
      proposalsRef,
      where('proposer_id', '==', currentUserId),
      where('status', '==', ProposalStatus.ACTIVE),
      limit(1),
    );

    const snapshot = await getDocs(q);
    if (snapshot.empty) {
      return null;
    }

    const doc = snapshot.docs[0];
    return {
      id: doc.id,
      ...doc.data(),
    } as ProposalEntity;
  } catch (error) {
    console.error('Error fetching user active proposal:', error);
    return null;
  }
};
