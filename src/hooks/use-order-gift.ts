import { useEffect, useState } from 'react';

import { getGiftById } from '@/api/gifts.api';
import type {
  GiftEntity,
  OrderEntity,
  OrderGift,
} from '@/mikerudenko/marketplace-shared';
import { globalCache } from '@/utils/cache-utils';

// Cache configuration for gifts
const GIFT_CACHE_CONFIG = { duration: 10 * 60 * 1000 }; // 10 minutes

// Helper function to convert GiftEntity to OrderGift
function giftEntityToOrderGift(giftEntity: GiftEntity): OrderGift {
  return {
    base_name: giftEntity.base_name,
    owned_gift_id: giftEntity.owned_gift_id,
    backdrop: giftEntity.backdrop,
    model: giftEntity.model,
    symbol: giftEntity.symbol,
  };
}

// Helper function to generate cache key
function getGiftCacheKey(giftId: string): string {
  return `order-gift:${giftId}`;
}

interface UseOrderGiftResult {
  gift: OrderGift | null;
  loading: boolean;
  error: string | null;
}

export function useOrderGift(
  order: OrderEntity | null | undefined,
): UseOrderGiftResult {
  const [gift, setGift] = useState<OrderGift | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Reset states when order changes
    setGift(null);
    setLoading(false);
    setError(null);

    if (!order || !order.giftId) {
      return;
    }

    const giftId = order.giftId;
    const cacheKey = getGiftCacheKey(giftId);

    // Check cache first
    const cachedGift = globalCache.get<OrderGift>(cacheKey);
    if (cachedGift) {
      setGift(cachedGift);
      return;
    }

    // Start loading
    setLoading(true);
    setError(null);

    getGiftById(giftId)
      .then((fetchedGift) => {
        if (fetchedGift) {
          const orderGift = giftEntityToOrderGift(fetchedGift);
          // Cache the result
          globalCache.set(cacheKey, orderGift, GIFT_CACHE_CONFIG);
          setGift(orderGift);
        } else {
          setGift(null);
        }
        setLoading(false);
      })
      .catch((err) => {
        console.error('Error fetching gift:', err);
        setError('Failed to load gift');
        setGift(null);
        setLoading(false);
      });
  }, [order?.giftId]);

  return { gift, loading, error };
}
