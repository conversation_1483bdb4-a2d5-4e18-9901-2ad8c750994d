import { log } from "./logger";
import { LogOperations } from "../constants/bot-commands";

export const BusinessConnectionHelpersLogger = {
  logGiftTransferSuccess(params: { chatId: string; owned_gift_id: string }) {
    log.info(`Successfully sent gift back to user ${params.chatId}`, {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      component: "transfer_gift",
      chatId: params.chatId,
      owned_gift_id: params.owned_gift_id,
    });
  },

  logGiftTransferFailed(params: {
    sendGiftResult: any;
    chatId: string;
    owned_gift_id: string;
  }) {
    log.error("Failed to transfer gift", params.sendGiftResult, {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      component: "transfer_gift",
      chatId: params.chatId,
      owned_gift_id: params.owned_gift_id,
    });
  },

  logGiftTransferError(params: {
    error: unknown;
    chatId: string;
    owned_gift_id: string;
  }) {
    log.error("Error transferring gift", params.error, {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      component: "transfer_gift",
      chatId: params.chatId,
      owned_gift_id: params.owned_gift_id,
    });
  },

  logBusinessAccountGiftsError(params: {
    error: unknown;
    businessConnectionId: string;
  }) {
    log.error("Error getting business account gifts", params.error, {
      operation: LogOperations.BUSINESS_CONNECTION_MIDDLEWARE,
      component: "get_business_account_gifts",
      businessConnectionId: params.businessConnectionId,
    });
  },
};
