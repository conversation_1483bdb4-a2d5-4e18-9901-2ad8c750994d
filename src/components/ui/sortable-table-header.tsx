'use client';

import { ChevronDown, ChevronsUpDown, ChevronUp } from 'lucide-react';

import { TableHead } from '@/components/ui/table';
import { cn } from '@/lib/utils';

export type SortDirection = 'asc' | 'desc' | null;

interface SortableTableHeaderProps {
  children: React.ReactNode;
  sortKey: string;
  currentSortKey?: string;
  currentSortDirection?: SortDirection;
  onSort: (key: string, direction: SortDirection) => void;
  className?: string;
}

export function SortableTableHeader({
  children,
  sortKey,
  currentSortKey,
  currentSortDirection,
  onSort,
  className,
}: SortableTableHeaderProps) {
  const isActive = currentSortKey === sortKey;
  const nextDirection: SortDirection = !isActive
    ? 'asc'
    : currentSortDirection === 'asc'
      ? 'desc'
      : currentSortDirection === 'desc'
        ? null
        : 'asc';

  const handleClick = () => {
    onSort(sortKey, nextDirection);
  };

  const getSortIcon = () => {
    if (!isActive) {
      return <ChevronsUpDown className="w-4 h-4 text-[#708499]" />;
    }

    if (currentSortDirection === 'asc') {
      return <ChevronUp className="w-4 h-4 text-[#6ab2f2]" />;
    }

    if (currentSortDirection === 'desc') {
      return <ChevronDown className="w-4 h-4 text-[#6ab2f2]" />;
    }

    return <ChevronsUpDown className="w-4 h-4 text-[#708499]" />;
  };

  return (
    <TableHead
      className={cn(
        'cursor-pointer select-none hover:bg-[#232e3c]/30 transition-colors',
        className,
      )}
      onClick={handleClick}
    >
      <div className="flex items-center gap-2">
        {children}
        {getSortIcon()}
      </div>
    </TableHead>
  );
}
