import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const generalOrderLogger = createServiceLogger("general-order-function");

export const GeneralOrderLogger = {
  logCancelOrderStarted({
    orderId,
    userId,
  }: {
    orderId: string;
    userId: string;
  }) {
    generalOrderLogger.logInfo(
      "Order cancellation started",
      LogOperations.CANCEL_ORDER,
      {
        orderId,
        userId,
      }
    );
  },

  logCancelOrderSuccess({
    orderId,
    userId,
    feeApplied,
    feeType,
  }: {
    orderId: string;
    userId: string;
    feeApplied: number;
    feeType: string;
  }) {
    generalOrderLogger.logInfo(
      "Order cancelled successfully",
      LogOperations.CANCEL_ORDER,
      {
        orderId,
        userId,
        feeApplied,
        feeType,
      }
    );
  },

  logCancelOrderError({
    error,
    orderId,
    userId,
  }: {
    error: unknown;
    orderId?: string;
    userId?: string;
  }) {
    generalOrderLogger.logError(
      "Error cancelling order",
      error,
      LogOperations.CANCEL_ORDER,
      {
        orderId,
        userId,
      }
    );
  },
};
