'use client';

import type { ReactNode } from 'react';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { COINGECKO_API_URL } from '@/core.constants';

interface TonExchangeRateContextType {
  tonToUsdRate: number | null;
  isLoading: boolean;
  lastUpdated: Date | null;
  convertTonToUsd: (tonAmount: number) => number;
}

const TonExchangeRateContext = createContext<
  TonExchangeRateContextType | undefined
>(undefined);

export const useTonExchangeRate = () => {
  const context = useContext(TonExchangeRateContext);
  if (context === undefined) {
    throw new Error(
      'useTonExchangeRate must be used within a TonExchangeRateProvider',
    );
  }
  return context;
};

const REFRESH_INTERVAL = 20 * 60 * 1000; // 20 minutes in milliseconds

export const TonExchangeRateProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [tonToUsdRate, setTonToUsdRate] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchExchangeRate = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(COINGECKO_API_URL);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const rate = data['the-open-network']?.usd;

      if (typeof rate === 'number') {
        setTonToUsdRate(rate);
        setLastUpdated(new Date());
      } else {
        console.error('Invalid exchange rate data received:', data);
      }
    } catch (error) {
      console.error('Failed to fetch TON exchange rate:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const convertTonToUsd = useCallback(
    (tonAmount: number): number => {
      if (tonToUsdRate === null) return 0;
      return tonAmount * tonToUsdRate;
    },
    [tonToUsdRate],
  );

  useEffect(() => {
    // Fetch immediately on mount
    fetchExchangeRate();

    // Set up interval to fetch every 20 minutes
    const interval = setInterval(fetchExchangeRate, REFRESH_INTERVAL);

    return () => clearInterval(interval);
  }, []);

  const contextValue = useMemo(
    () => ({
      tonToUsdRate,
      isLoading,
      lastUpdated,
      convertTonToUsd,
    }),
    [tonToUsdRate, isLoading, lastUpdated, convertTonToUsd],
  );

  return (
    <TonExchangeRateContext.Provider value={contextValue}>
      {children}
    </TonExchangeRateContext.Provider>
  );
};
