import { createServiceLogger } from "../logger/logger";
import { LogOperations } from "../constants";

const adminCollectionLogger = createServiceLogger("admin-collection-function");

export const AdminCollectionLogger = {
  logAdminCollectionOperation({
    operation,
    collectionId,
    updatedCount,
    userId,
  }: {
    operation: LogOperations;
    collectionId: string;
    updatedCount: number;
    userId: string;
  }) {
    adminCollectionLogger.logInfo(
      `Admin collection operation: ${operation} for collection ${collectionId}, updated ${updatedCount} orders`,
      operation,
      {
        collectionId,
        updatedCount,
        userId,
      }
    );
  },
};

export function logAdminCollectionOperation({
  operation,
  collectionId,
  updatedCount,
  userId,
}: {
  operation: LogOperations;
  collectionId: string;
  updatedCount: number;
  userId: string;
}) {
  adminCollectionLogger.logInfo(
    `Admin collection operation: ${operation} for collection ${collectionId}, updated ${updatedCount} orders`,
    operation,
    {
      collectionId,
      updatedCount,
      userId,
    }
  );
}

export function logAdminCollectionError({
  error,
  operation,
  collectionId,
  userId,
}: {
  error: unknown;
  operation: LogOperations;
  collectionId?: string;
  userId?: string;
}) {
  adminCollectionLogger.logError(
    `Error in admin collection operation: ${operation}`,
    error,
    operation,
    {
      collectionId,
      userId,
    }
  );
}
