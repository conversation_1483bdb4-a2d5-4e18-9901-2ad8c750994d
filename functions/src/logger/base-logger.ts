import { log } from "./logger";
import { LogOperations } from "../constants";

export interface LogContext {
  userId?: string;
  orderId?: string;
  transactionId?: string;
  collectionId?: string;
  operation?: string;
  [key: string]: any;
}

// Pure function to create log context
export function createLogContext(
  serviceName: string,
  operation: LogOperations,
  additionalContext?: LogContext
): LogContext {
  return {
    service: serviceName,
    operation,
    ...additionalContext,
  };
}

// Higher-order function that creates logger functions for a specific service
export function createServiceLogger(serviceName: string) {
  const createContext = (operation: LogOperations, context?: LogContext) =>
    createLogContext(serviceName, operation, context);

  return {
    logInfo: (
      message: string,
      operation: LogOperations,
      context?: LogContext
    ) => {
      log.info(message, createContext(operation, context));
    },

    logError: (
      message: string,
      error: unknown,
      operation: LogOperations,
      context?: LogContext
    ) => {
      log.error(message, error, createContext(operation, context));
    },

    logWarn: (
      message: string,
      operation: LogOperations,
      context?: LogContext
    ) => {
      log.warn(message, createContext(operation, context));
    },

    logDebug: (
      message: string,
      operation: LogOperations,
      context?: LogContext
    ) => {
      log.debug(message, createContext(operation, context));
    },
  };
}

// Individual logging functions that can be composed
export function logInfo(
  serviceName: string,
  message: string,
  operation: LogOperations,
  context?: LogContext
) {
  log.info(message, createLogContext(serviceName, operation, context));
}

export function logError(
  serviceName: string,
  message: string,
  error: unknown,
  operation: LogOperations,
  context?: LogContext
) {
  log.error(message, error, createLogContext(serviceName, operation, context));
}

export function logWarn(
  serviceName: string,
  message: string,
  operation: LogOperations,
  context?: LogContext
) {
  log.warn(message, createLogContext(serviceName, operation, context));
}

export function logDebug(
  serviceName: string,
  message: string,
  operation: LogOperations,
  context?: LogContext
) {
  log.debug(message, createLogContext(serviceName, operation, context));
}
