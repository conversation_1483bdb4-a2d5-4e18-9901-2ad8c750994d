import { log } from "./logger";
import { LogOperations } from "../constants/bot-commands";

export const GiftValidationLogger = {
  logCdnMappingFetchFailed(params: {
    component: string;
    orderCollectionId?: string;
  }) {
    log.warn(
      "Failed to fetch collection mapping from CDN, skipping gift validation for CREATED order",
      {
        operation: LogOperations.GIFT_VALIDATION,
        ...params,
      }
    );
  },

  logCollectionNotFound(params: {
    component: string;
    orderCollectionId: string;
  }) {
    log.error(
      `Collection name not found for ID: ${params.orderCollectionId}`,
      undefined,
      {
        operation: LogOperations.GIFT_VALIDATION,
        ...params,
      }
    );
  },

  logGiftValidationStarted(params: {
    component: string;
    orderCollectionId: string;
    collectionName: string;
    giftBaseName: string;
  }) {
    log.info("Validating gift for CREATED order", {
      operation: LogOperations.GIFT_VALIDATION,
      ...params,
    });
  },

  logGiftNameMatchesCollection(params: {
    component: string;
    orderCollectionId: string;
    collectionName: string;
    giftBaseName: string;
  }) {
    log.warn(
      "Gift base name matches collection name - user trying to trick us",
      {
        operation: LogOperations.GIFT_VALIDATION,
        ...params,
      }
    );
  },

  logGiftValidationError(params: {
    component: string;
    orderCollectionId: string;
    error: unknown;
  }) {
    log.error("Error validating gift for CREATED order", params.error, {
      operation: LogOperations.GIFT_VALIDATION,
      component: params.component,
      orderCollectionId: params.orderCollectionId,
    });
  },

  logGiftValidationSuccess(params: {
    component: string;
    orderCollectionId: string;
    collectionName: string;
    giftBaseName: string;
  }) {
    log.info("Gift validation successful for CREATED order", {
      operation: LogOperations.GIFT_VALIDATION,
      ...params,
    });
  },

  logCdnFetchError(params: { component: string; error: unknown }) {
    log.error("Error fetching collection mapping from CDN", params.error, {
      operation: LogOperations.GIFT_VALIDATION,
      component: params.component,
    });
  },

  logGiftValidationForOrderStarted(params: {
    component: string;
    orderCollectionId: string;
    giftId?: string;
  }) {
    log.info("Starting gift validation for order", {
      operation: LogOperations.GIFT_VALIDATION,
      ...params,
    });
  },

  logGiftValidationForOrderSuccess(params: {
    component: string;
    orderCollectionId: string;
    isValid: boolean;
  }) {
    log.info("Gift validation completed for order", {
      operation: LogOperations.GIFT_VALIDATION,
      ...params,
    });
  },

  logIdToNameMappingFetchError(params: { error: unknown }) {
    log.error("Error fetching id-to-name mapping from CDN", params.error, {
      operation: LogOperations.GIFT_VALIDATION,
      component: "fetch_id_to_name_mapping",
    });
  },

  logAttributeDataFetchError(params: {
    error: unknown;
    attributeType: string;
    collectionName: string;
  }) {
    log.error(
      `Error fetching ${params.attributeType} data for collection ${params.collectionName} from CDN`,
      params.error,
      {
        operation: LogOperations.GIFT_VALIDATION,
        component: "fetch_attribute_data",
        attributeType: params.attributeType,
        collectionName: params.collectionName,
      }
    );
  },

  logInvalidAttribute(params: {
    attributeType: string;
    giftAttributeName: string;
    giftRarityPermille: number;
    collectionName?: string;
  }) {
    log.error(
      `Invalid ${params.attributeType}: ${params.giftAttributeName} with rarity ${params.giftRarityPermille} not found`,
      undefined,
      {
        operation: LogOperations.GIFT_VALIDATION,
        component: "validate_attribute",
        attributeType: params.attributeType,
        giftAttributeName: params.giftAttributeName,
        giftRarityPermille: params.giftRarityPermille,
        collectionName: params.collectionName,
      }
    );
  },

  logCollectionMappingFetchFailed(params: { orderCollectionId: string }) {
    log.warn(
      "Failed to fetch collection mapping from CDN, skipping gift validation",
      {
        operation: LogOperations.GIFT_VALIDATION,
        component: "validate_sent_gift_with_order",
        orderCollectionId: params.orderCollectionId,
      }
    );
  },

  logCollectionNameNotFound(params: { orderCollectionId: string }) {
    log.error(
      `Collection name not found for ID: ${params.orderCollectionId}`,
      undefined,
      {
        operation: LogOperations.GIFT_VALIDATION,
        component: "validate_sent_gift_with_order",
        orderCollectionId: params.orderCollectionId,
      }
    );
  },

  logModelsDataFetchFailed(params: { collectionName: string }) {
    log.warn("Failed to fetch models data from CDN, skipping gift validation", {
      operation: LogOperations.GIFT_VALIDATION,
      component: "validate_sent_gift_with_order",
      collectionName: params.collectionName,
    });
  },

  logPatternsDataFetchFailed(params: { collectionName: string }) {
    log.warn(
      "Failed to fetch patterns data from CDN, skipping gift validation",
      {
        operation: LogOperations.GIFT_VALIDATION,
        component: "validate_sent_gift_with_order",
        collectionName: params.collectionName,
      }
    );
  },

  logBackdropsDataFetchFailed(params: { collectionName: string }) {
    log.warn(
      "Failed to fetch backdrops data from CDN, skipping gift validation",
      {
        operation: LogOperations.GIFT_VALIDATION,
        component: "validate_sent_gift_with_order",
        collectionName: params.collectionName,
      }
    );
  },

  logBackdropValidationSuccess(params: { collectionName: string }) {
    log.info("Backdrop is valid", {
      operation: LogOperations.GIFT_VALIDATION,
      component: "validate_sent_gift_with_order",
      collectionName: params.collectionName,
    });
  },

  logGiftValidationFailed(params: {
    error: unknown;
    orderCollectionId: string;
  }) {
    log.error("Gift validation failed", params.error, {
      operation: LogOperations.GIFT_VALIDATION,
      component: "validate_sent_gift_with_order",
      orderCollectionId: params.orderCollectionId,
    });
  },
};
