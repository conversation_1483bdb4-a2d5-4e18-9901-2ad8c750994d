import { useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';
import {
  canCancelOrder,
  canCreateSecondaryMarketOrder,
  hasSecondaryMarketPrice,
} from '@/services/order-service';

import { userOrderActionsSectionMessages } from './intl/user-order-actions-section.messages';

interface UserOrderActionsSectionProps {
  order: OrderEntity;
  currentUserId?: string;
  onCancelOrder: () => void;
  onCreateSecondaryMarketOrder: () => void;
  onShowResellHistory?: () => void;
}

export function UserOrderActionsSection({
  order,
  currentUserId,
  onCancelOrder,
  onCreateSecondaryMarketOrder,
  onShowResellHistory,
}: UserOrderActionsSectionProps) {
  const { formatMessage: t } = useIntl();
  const canCancel = canCancelOrder(order, currentUserId);
  const canCreateSecondary = canCreateSecondaryMarketOrder(
    order,
    currentUserId,
  );
  const hasSecondaryPrice = hasSecondaryMarketPrice(order);
  // TODO: Temporarily hidden - resell tx history feature
  const shouldShowResellHistory = false;
  // order.secondaryMarketPrice && order.secondaryMarketPrice > 0;

  if (!canCancel && !canCreateSecondary && !shouldShowResellHistory) {
    return null;
  }

  const buttons = [];

  if (canCreateSecondary) {
    buttons.push(
      <Button
        key="resale"
        onClick={onCreateSecondaryMarketOrder}
        className="flex-1 rounded-xl py-3 font-semibold bg-[#6ab2f2] hover:bg-[#5a9fd9] text-white"
      >
        {hasSecondaryPrice
          ? t(userOrderActionsSectionMessages.updateResaleOrder)
          : t(userOrderActionsSectionMessages.createResaleOrder)}
      </Button>,
    );
  }

  if (shouldShowResellHistory && onShowResellHistory) {
    buttons.push(
      <Button
        key="history"
        onClick={onShowResellHistory}
        variant="outline"
        className="flex-1 rounded-xl py-3 font-semibold border-[#6ab2f2] text-[#6ab2f2] hover:bg-[#6ab2f2]/10"
      >
        {t(userOrderActionsSectionMessages.showResellHistory)}
      </Button>,
    );
  }

  if (canCancel) {
    buttons.push(
      <Button
        key="cancel"
        variant="destructive"
        onClick={onCancelOrder}
        className="flex-1 rounded-xl py-3 font-semibold"
      >
        {t(userOrderActionsSectionMessages.cancelOrder)}
      </Button>,
    );
  }

  if (buttons.length === 0) {
    return null;
  }

  return (
    <div className="pt-6 border-t border-[#3a4a5c]/30">
      <div className="flex gap-3">{buttons}</div>
    </div>
  );
}
