import { HttpsError, onCall } from "firebase-functions/v2/https";
import { getTelegramBotToken, isDevelopment } from "../config";
import { commonFunctionsConfig } from "../constants";
import { log } from "../logger/logger";
import {
  validateTelegramData,
  findOrCreateUser,
  createFirebaseAuthUser,
} from "./telegram-auth-function.service";

import {
  throwInitDataRequired,
  throwBotTokenNotConfigured,
  throwAuthenticationFailed,
} from "./telegram-auth-function.error-handler";
import { TelegramAuthLogger } from "./telegram-auth-function.logger";

export const signInWithTelegram = onCall<{
  initData: string;
  useLocalBotToken?: boolean;
}>(commonFunctionsConfig, async (request) => {
  TelegramAuthLogger.logAuthStarted();

  try {
    const isDevMode = isDevelopment();
    const { initData, useLocalBotToken } = request.data;

    if (!initData) {
      throwInitDataRequired();
    }

    log.info("Telegram authentication mode", {
      operation: "telegram_signin",
      isDevMode,
    });

    // Get bot token from Firebase config
    const botToken = getTelegramBotToken(useLocalBotToken);
    if (!botToken) {
      throwBotTokenNotConfigured();
    }

    // Validate and extract Telegram user data
    const { telegramUser, telegramId } = await validateTelegramData(
      initData,
      botToken,
      isDevMode
    );

    log.info("Final user data validated", {
      operation: "telegram_signin",
      telegramId,
      firstName: telegramUser.first_name,
      username: telegramUser.username,
    });

    // Find or create user
    const { userId, userRecord } = await findOrCreateUser(
      telegramUser,
      telegramId
    );

    // Log user creation if it's a new user
    if (!userRecord.id || userRecord.id === userId) {
      TelegramAuthLogger.logUserCreated({
        userId,
        telegramId,
      });
    }

    // Create Firebase Auth user and custom token
    const authResult = await createFirebaseAuthUser(
      userId,
      userRecord,
      telegramUser,
      telegramId
    );

    return {
      customToken: authResult.customToken,
      user: userRecord,
    };
  } catch (error) {
    TelegramAuthLogger.logAuthError({
      error,
      requestData: request.data,
    });

    if (error instanceof HttpsError) {
      throw error;
    }

    throwAuthenticationFailed();
  }
});
