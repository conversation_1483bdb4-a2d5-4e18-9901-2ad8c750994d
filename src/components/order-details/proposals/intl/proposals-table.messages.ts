import { defineMessages } from 'react-intl';

export const proposalsTableMessages = defineMessages({
  priceProposals: {
    id: 'proposalsTable.priceProposals',
    defaultMessage: 'Price Proposals',
  },
  noProposals: {
    id: 'proposalsTable.noProposals',
    defaultMessage: 'No price proposals yet',
  },
  yourProposal: {
    id: 'proposalsTable.yourProposal',
    defaultMessage: 'Your Proposal',
  },
  statusActive: {
    id: 'proposalsTable.statusActive',
    defaultMessage: 'Active',
  },
  statusCancelled: {
    id: 'proposalsTable.statusCancelled',
    defaultMessage: 'Cancelled',
  },
  statusAccepted: {
    id: 'proposalsTable.statusAccepted',
    defaultMessage: 'Accepted',
  },
  accept: {
    id: 'proposalsTable.accept',
    defaultMessage: 'Accept',
  },
  accepting: {
    id: 'proposalsTable.accepting',
    defaultMessage: 'Accepting...',
  },
  cancel: {
    id: 'proposalsTable.cancel',
    defaultMessage: 'Cancel',
  },
  cancelling: {
    id: 'proposalsTable.cancelling',
    defaultMessage: 'Cancelling...',
  },
  loadMore: {
    id: 'proposalsTable.loadMore',
    defaultMessage: 'Load More Proposals',
  },
  loadingMore: {
    id: 'proposalsTable.loadingMore',
    defaultMessage: 'Loading...',
  },
  // Toast messages
  proposalCreatedSuccess: {
    id: 'proposalsTable.proposalCreatedSuccess',
    defaultMessage: 'Price proposal created successfully',
  },
  proposalCancelledSuccess: {
    id: 'proposalsTable.proposalCancelledSuccess',
    defaultMessage: 'Price proposal cancelled successfully',
  },
  proposalAcceptedSuccess: {
    id: 'proposalsTable.proposalAcceptedSuccess',
    defaultMessage: 'Price proposal accepted successfully',
  },
});
