import * as admin from "firebase-admin";
import { HttpsError } from "firebase-functions/v2/https";
import { hasAvailableBalance } from "./balance-service/balance-service";
import { getAppConfig, calculateFeeAmount } from "./fee-service/fee-service";

import { safeMultiply, bpsToDecimal } from "../utils";
import { ORDER_ERRORS } from "../error-messages";
import {
  COLLECTION_NAME,
  CollectionEntity,
  CollectionStatus,
  OrderEntity,
  OrderFees,
  ORDERS_COLLECTION_NAME,
  OrderStatus,
  UserType,
} from "../mikerudenko/marketplace-shared";

export async function validateCollectionAndFloorPrice(params: {
  db: admin.firestore.Firestore;
  collectionId: string;
  amount: number;
}) {
  const { db, collectionId, amount } = params;
  const collectionDoc = await db
    .collection(COLLECTION_NAME)
    .doc(collectionId)
    .get();

  if (!collectionDoc.exists) {
    throw new HttpsError("not-found", "Collection not found.");
  }

  const collection = collectionDoc.data() as CollectionEntity;

  // Check if collection is active
  if (collection.active === false) {
    throw new HttpsError(
      "failed-precondition",
      "This collection is not active for order creation."
    );
  }

  if (amount < collection?.floorPrice) {
    throw new HttpsError(
      "invalid-argument",
      `Order amount must be at least ${collection?.floorPrice} TON (collection floor price).`
    );
  }

  return collection;
}

/**
 * Validates that buyers cannot create orders for MARKET collections
 * @param collection - The collection entity
 * @param userType - The user type (BUYER or SELLER)
 * @throws HttpsError if buyer tries to create order for MARKET collection
 */
export function validateBuyerMarketCollectionRestriction(
  collection: CollectionEntity,
  userType: UserType
): void {
  if (
    userType === UserType.BUYER &&
    collection.status === CollectionStatus.MARKET
  ) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.BUYERS_CANNOT_CREATE_MARKET_ORDERS,
        fallbackMessage:
          "Buyers cannot create orders for market collections. Only sellers can create orders for market collections.",
      })
    );
  }
}

export async function validateBalanceAndCalculateLock(params: {
  userId: string;
  amount: number;
  userType: UserType;
  orderFees?: OrderFees;
}) {
  const { userId, amount, userType, orderFees } = params;
  let lockPercentageBPS: number;

  if (orderFees) {
    // Use fees from existing order
    lockPercentageBPS =
      userType === UserType.BUYER
        ? orderFees.buyer_locked_percentage
        : orderFees.seller_locked_percentage;
  } else {
    // Use current app config for new orders
    const config = await getAppConfig();
    lockPercentageBPS =
      userType === UserType.BUYER
        ? config?.buyer_lock_percentage
        : config?.seller_lock_percentage;
  }

  // Convert BPS to decimal
  const lockPercentage = bpsToDecimal(lockPercentageBPS);
  const lockedAmount = safeMultiply(amount, lockPercentage);

  // For buyers, also validate they have sufficient balance for purchase fees
  let totalRequiredAmount = lockedAmount;
  let purchaseFeeAmount = 0;

  if (userType === UserType.BUYER) {
    let purchaseFeeBPS = 0;

    if (orderFees) {
      // Use fees from existing order
      purchaseFeeBPS = orderFees.purchase_fee ?? 0;
    } else {
      // Use current app config for new orders
      const config = await getAppConfig();
      purchaseFeeBPS = config?.purchase_fee ?? 0;
    }

    if (purchaseFeeBPS > 0) {
      purchaseFeeAmount = calculateFeeAmount(amount, purchaseFeeBPS);
      totalRequiredAmount = lockedAmount + purchaseFeeAmount;
    }
  }

  const hasBalance = await hasAvailableBalance(userId, totalRequiredAmount);
  if (!hasBalance) {
    const feeMessage =
      purchaseFeeAmount > 0 ? ` + ${purchaseFeeAmount} TON purchase fee` : "";

    throw new HttpsError(
      "failed-precondition",
      `Insufficient balance. You need at least ${totalRequiredAmount} TON available (${lockedAmount} TON collateral${feeMessage}).`
    );
  }

  return { lockedAmount, lockPercentage };
}

export async function validateOrderCreation(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    collectionId: string;
    price: number;
    userType: UserType;
  }
) {
  const { userId, collectionId, price, userType } = params;

  const collection = await validateCollectionAndFloorPrice({
    db,
    collectionId,
    amount: price,
  });

  // Validate that buyers cannot create orders for MARKET collections
  validateBuyerMarketCollectionRestriction(collection, userType);

  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock({
      userId,
      amount: price,
      userType,
    });

  return { collection, lockedAmount, lockPercentage };
}

export async function validateOrderCreationForMarketCollection(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    collectionId: string;
    price: number;
    userType: UserType;
  }
) {
  const { userId, collectionId, price, userType } = params;

  const collection = await validateCollectionAndFloorPrice({
    db,
    collectionId,
    amount: price,
  });

  // Validate that buyers cannot create orders for MARKET collections
  validateBuyerMarketCollectionRestriction(collection, userType);

  // For sellers in MARKET collections, skip balance validation and return 0 amounts
  if (userType === UserType.SELLER) {
    return { collection, lockedAmount: 0, lockPercentage: 0 };
  }

  // For buyers, still validate balance and calculate lock
  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock({
      userId,
      amount: price,
      userType,
    });

  return { collection, lockedAmount, lockPercentage };
}

export async function getAndValidateOrder(
  db: admin.firestore.Firestore,
  orderId: string
): Promise<OrderEntity> {
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throw new HttpsError("not-found", "Order not found.");
  }

  return { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;
}

export function validateOrderAvailableForPurchase(order: OrderEntity): void {
  if (order.status !== OrderStatus.ACTIVE) {
    throw new HttpsError(
      "failed-precondition",
      "Order is not available for purchase."
    );
  }
}

export function validateBuyerPurchaseConstraints(
  order: OrderEntity,
  buyerId: string
) {
  // Check if order already has a buyer
  if (order.buyerId && order.buyerId !== buyerId) {
    throw new HttpsError("failed-precondition", "Order already has a buyer.");
  }

  // Check if buyer is trying to buy their own order
  if (order.sellerId === buyerId) {
    throw new HttpsError(
      "failed-precondition",
      "You cannot buy your own order."
    );
  }
}

export function validateSellerPurchaseConstraints(
  order: OrderEntity,
  sellerId: string
) {
  // Check if order already has a seller
  if (order.sellerId && order.sellerId !== sellerId) {
    throw new HttpsError("failed-precondition", "Order already has a seller.");
  }

  // Check if seller is trying to sell to their own order
  if (order.buyerId === sellerId) {
    throw new HttpsError(
      "failed-precondition",
      "You cannot sell to your own order."
    );
  }
}

export async function validateBuyerPurchase(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    orderId: string;
  }
) {
  const { userId, orderId } = params;
  const order = await getAndValidateOrder(db, orderId);

  validateOrderAvailableForPurchase(order);
  validateBuyerPurchaseConstraints(order, userId);

  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock({
      userId,
      amount: order.price,
      userType: UserType.BUYER,
      orderFees: order.fees,
    });

  return { order, lockedAmount, lockPercentage };
}

export async function validateSellerPurchase(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    orderId: string;
  }
) {
  const { userId, orderId } = params;
  const order = await getAndValidateOrder(db, orderId);

  validateOrderAvailableForPurchase(order);
  validateSellerPurchaseConstraints(order, userId);

  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock({
      userId,
      amount: order.price,
      userType: UserType.SELLER,
      orderFees: order.fees,
    });

  return { order, lockedAmount, lockPercentage };
}

/**
 * Validates that a seller doesn't have too many orders with "created" status for market collections
 * @param db - Firestore database instance
 * @param sellerId - ID of the seller
 * @param collectionId - ID of the collection
 * @throws HttpsError if seller has 3 or more orders with "created" status
 */
export async function validateSellerCreatedOrdersLimit(
  db: admin.firestore.Firestore,
  sellerId: string,
  collectionId: string
): Promise<void> {
  // Get the collection to check if it's a MARKET collection
  const collectionDoc = await db
    .collection(COLLECTION_NAME)
    .doc(collectionId)
    .get();

  if (!collectionDoc.exists) {
    throw new HttpsError("not-found", "Collection not found.");
  }

  const collection = collectionDoc.data() as CollectionEntity;

  // Only apply this validation for MARKET collections
  if (collection.status !== CollectionStatus.MARKET) {
    return;
  }

  // Count existing orders with "created" status for this seller
  const createdOrdersQuery = db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", sellerId)
    .where("status", "==", OrderStatus.CREATED);

  const createdOrdersSnapshot = await createdOrdersQuery.get();
  const createdOrdersCount = createdOrdersSnapshot.size;

  // If seller already has 3 or more orders with "created" status, prevent creating more
  if (createdOrdersCount >= 3) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.TOO_MANY_CREATED_ORDERS,
        fallbackMessage:
          "You already have 3 orders that need to be activated. Please activate existing orders before creating new ones.",
      })
    );
  }
}
