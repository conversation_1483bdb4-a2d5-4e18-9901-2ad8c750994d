import { createServiceLogger } from "../logger/base-logger";
import { LogOperations } from "../constants";

// Create service logger using functional composition
const botHealthCheckLogger = createServiceLogger("bot-health-check-function");

export function logHealthCheckStarted({ status }: { status: string }) {
  botHealthCheckLogger.logInfo(
    "Bot health check started",
    LogOperations.BOT_HEALTH_CHECK,
    {
      status,
    }
  );
}

export function logHealthCheckCall({
  healthCheckUrl,
  hasAuthentication,
}: {
  healthCheckUrl: string;
  hasAuthentication: boolean;
}) {
  botHealthCheckLogger.logInfo(
    "Making health check call",
    LogOperations.BOT_HEALTH_CHECK,
    {
      healthCheckUrl,
      hasAuthentication,
    }
  );
}

export function logHealthCheckResponse({
  responseData,
}: {
  responseData: any;
}) {
  botHealthCheckLogger.logInfo(
    "Health check response received",
    LogOperations.BOT_HEALTH_CHECK,
    { responseData }
  );
}

export function logHealthCheckPassed({ status }: { status: string }) {
  botHealthCheckLogger.logInfo(
    "Bot health check passed",
    LogOperations.BOT_HEALTH_CHECK,
    {
      status,
    }
  );
}

export function logHealthCheckUnhealthy({
  status,
  responseData,
}: {
  status: string;
  responseData: any;
}) {
  botHealthCheckLogger.logInfo(
    "Bot health check unhealthy",
    LogOperations.BOT_HEALTH_CHECK,
    {
      status,
      responseData,
    }
  );
}

export function logHealthCheckFailed({
  error,
  status,
}: {
  error: unknown;
  status: string;
}) {
  botHealthCheckLogger.logError(
    "Bot health check failed",
    error,
    LogOperations.BOT_HEALTH_CHECK,
    {
      status,
    }
  );
}

export function logMonitorTriggered({
  status,
  timestamp,
}: {
  status: string;
  timestamp: string;
}) {
  botHealthCheckLogger.logInfo(
    "Bot health monitor triggered",
    LogOperations.MONITOR,
    {
      status,
      timestamp,
    }
  );
}

export function logMonitorCompleted() {
  botHealthCheckLogger.logInfo(
    "Bot health monitor completed",
    LogOperations.MONITOR,
    {}
  );
}

export function logMonitorFailed({
  error,
  status,
}: {
  error: unknown;
  status: string;
}) {
  botHealthCheckLogger.logError(
    "Bot health monitor failed",
    error,
    LogOperations.MONITOR,
    {
      status,
    }
  );
}
