import { LogOperations } from "../constants/bot-commands";
import { log } from "../utils/logger";

export const NotificationServiceLogger = {
  logSellerNotificationStart(params: {
    orderId: string;
    sellerId: string;
    orderNumber?: number;
    price?: number;
    proposedPrice?: number;
    originalPrice?: number;
  }) {
    log.info("Notifying seller of order payment", {
      operation: LogOperations.NOTIFY_SELLER_ORDER_PAID,
      ...params,
    });
  },

  logSellerNotificationSuccess(params: {
    orderId: string;
    sellerId: string;
    tgId: string;
  }) {
    log.info("Seller notification sent successfully", {
      operation: LogOperations.NOTIFY_SELLER_ORDER_PAID,
      ...params,
    });
  },

  logSellerNotificationError(
    error: unknown,
    params: {
      orderId: string;
      sellerId: string;
    }
  ) {
    log.error("Failed to notify seller of order payment", error, {
      operation: LogOperations.NOTIFY_SELLER_ORDER_PAID,
      ...params,
    });
  },

  logBuyerNotificationStart(params: {
    orderId: string;
    buyerId: string;
    orderNumber?: number;
  }) {
    log.info("Notifying buyer of gift sent", {
      operation: LogOperations.NOTIFY_BUYER_GIFT_SENT,
      ...params,
    });
  },

  logBuyerNotificationSuccess(params: {
    orderId: string;
    buyerId: string;
    tgId: string;
  }) {
    log.info("Buyer notification sent successfully", {
      operation: LogOperations.NOTIFY_BUYER_GIFT_SENT,
      ...params,
    });
  },

  logBuyerNotificationError(
    error: unknown,
    params: {
      orderId: string;
      buyerId: string;
    }
  ) {
    log.error("Failed to notify buyer of gift sent", error, {
      operation: LogOperations.NOTIFY_BUYER_GIFT_SENT,
      ...params,
    });
  },
};
