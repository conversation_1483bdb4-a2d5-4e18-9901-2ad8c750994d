import { doc, getDoc } from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import { AppCloudFunctions } from '@/core.constants';
import {
  APP_USERS_COLLECTION,
  MARKETPLACE_REVENUE_USER_ID,
} from '@/mikerudenko/marketplace-shared';
import { firebaseFunctions, firestore } from '@/root-context';

export interface MarketplaceRevenue {
  sum: number;
  locked: number;
}

export interface WithdrawRevenueData {
  withdrawAmount: number;
  johnDowWallet: string;
}

export interface WithdrawRevenueResponse {
  success: boolean;
  message: string;
  totalAmount: number;
}

export const getMarketplaceRevenue = async (): Promise<MarketplaceRevenue> => {
  try {
    const revenueDoc = await getDoc(
      doc(firestore, APP_USERS_COLLECTION, MARKETPLACE_REVENUE_USER_ID),
    );

    if (revenueDoc.exists()) {
      const data = revenueDoc.data();
      return data.balance ?? { sum: 0, locked: 0 };
    }

    return { sum: 0, locked: 0 };
  } catch (error) {
    console.error('Error loading marketplace revenue:', error);
    throw error;
  }
};

export const withdrawRevenue = async (data: WithdrawRevenueData) => {
  try {
    const withdrawRevenueFunction = httpsCallable<
      WithdrawRevenueData,
      WithdrawRevenueResponse
    >(firebaseFunctions, AppCloudFunctions.withdrawRevenue);

    const result = await withdrawRevenueFunction(data);
    return result.data;
  } catch (error) {
    console.error('Error withdrawing revenue:', error);
    throw error;
  }
};
