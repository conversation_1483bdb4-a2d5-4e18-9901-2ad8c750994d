import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const tonWalletServiceLogger = createServiceLogger("ton-wallet-service");

export function logWalletValidation({
  walletAddress,
  isValid,
}: {
  walletAddress: string;
  isValid: boolean;
}) {
  tonWalletServiceLogger.logInfo(
    "TON wallet validation completed",
    LogOperations.TON_WALLET,
    {
      walletAddress,
      isValid,
    }
  );
}

export function logWalletBinding({
  userId,
  walletAddress,
}: {
  userId: string;
  walletAddress: string;
}) {
  tonWalletServiceLogger.logInfo(
    "TON wallet bound to user",
    LogOperations.TON_WALLET,
    {
      userId,
      walletAddress,
    }
  );
}

export function logTonWalletServiceError({
  error,
  operation,
  userId,
  walletAddress,
}: {
  error: unknown;
  operation: string;
  userId?: string;
  walletAddress?: string;
}) {
  tonWalletServiceLogger.logError(
    `Error in TON wallet service: ${operation}`,
    error,
    LogOperations.TON_WALLET,
    {
      operation,
      userId,
      walletAddress,
    }
  );
}
