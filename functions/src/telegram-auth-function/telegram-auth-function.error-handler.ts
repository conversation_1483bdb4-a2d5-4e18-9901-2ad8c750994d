import { HttpsError } from "firebase-functions/v2/https";
import { GENERIC_ERRORS, TELEGRAM_ERRORS } from "../error-messages";

export function throwInitDataRequired(): never {
  throw new HttpsError(
    "invalid-argument",
    JSON.stringify({
      errorKey: TELEGRAM_ERRORS.INIT_DATA_REQUIRED,
      fallbackMessage: "initData is required",
    })
  );
}

export function throwBotTokenNotConfigured(): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: TELEGRAM_ERRORS.BOT_TOKEN_NOT_CONFIGURED,
      fallbackMessage: "Telegram bot token not configured",
    })
  );
}

export function throwInvalidTelegramData(error?: string): never {
  throw new HttpsError(
    "permission-denied",
    JSON.stringify({
      errorKey: TELEGRAM_ERRORS.INVALID_TELEGRAM_DATA,
      fallbackMessage: error ?? "Invalid Telegram data",
    })
  );
}

export function throwUserDataMissing(): never {
  throw new HttpsError(
    "invalid-argument",
    "User data is missing in mock initData"
  );
}

export function throwIAMPermissionError(): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: TELEGRAM_ERRORS.IAM_PERMISSION_ERROR,
      fallbackMessage:
        "Firebase service account lacks required IAM permissions for custom token creation. Please grant 'Service Account Token Creator' role.",
    })
  );
}

export function throwFirebaseAuthError(message?: string): never {
  throw new HttpsError(
    "internal",
    JSON.stringify({
      errorKey: TELEGRAM_ERRORS.FIREBASE_AUTH_ERROR,
      fallbackMessage: `Firebase Auth error: ${message ?? "Unknown error"}`,
    })
  );
}

export function throwAuthenticationFailed(): never {
  throw new HttpsError(
    "internal",
    JSON.stringify({
      errorKey: GENERIC_ERRORS.AUTHENTICATION_FAILED,
      fallbackMessage: "Authentication failed",
    })
  );
}
