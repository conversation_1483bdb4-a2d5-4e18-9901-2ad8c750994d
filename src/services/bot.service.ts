import bot from "../bot";
import { APP_NAME } from "../app.constants";
import { log } from "../utils/logger";

export class BotService {
  private static cachedBotUsername: string | null = null;

  static async getBotUsername(): Promise<string> {
    if (this.cachedBotUsername) {
      return this.cachedBotUsername;
    }

    try {
      const botInfo = await bot.telegram.getMe();
      this.cachedBotUsername = botInfo.username || `${APP_NAME.toLowerCase()}bot`;
      return this.cachedBotUsername;
    } catch (error) {
      log.error("Failed to get bot username", error);
      return `${APP_NAME.toLowerCase()}bot`; // fallback
    }
  }
}
