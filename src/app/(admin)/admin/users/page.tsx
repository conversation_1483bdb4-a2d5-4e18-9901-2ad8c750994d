'use client';

import { useAdminAuth } from '@/hooks/use-admin-auth';

import { UsersManagement } from '../users-management';

export default function UsersPage() {
  const { isAuthorized } = useAdminAuth();

  if (!isAuthorized) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Users</h1>
        <p className="text-muted-foreground">
          Manage user accounts and balance operations
        </p>
      </div>

      <UsersManagement />
    </div>
  );
}
