import {
  GiftEntity,
  GiftStatus,
} from "../../../miker<PERSON>nko/marketplace-shared";

// Type for gift data received from bot
export type GiftFromBot = Omit<
  GiftEntity,
  "id" | "createdAt" | "updatedAt" | "collectionId" | "owner_tg_id"
>;
import { createGift } from "../shared/gift-service";
import { log } from "../../../utils/logger";

async function handleDirectGiftDepositFlow(
  gift: GiftFromBot,
  userTgId: string,
  collectionId?: string
) {
  try {
    // Create gift in separate collection without order association
    const giftData = {
      owner_tg_id: userTgId,
      collectionId: collectionId || "",
      status: GiftStatus.DEPOSITED,
      base_name: gift.base_name,
      owned_gift_id: gift.owned_gift_id,
      model: gift.model,
      symbol: gift.symbol,
      backdrop: gift.backdrop,
    };

    const giftId = await createGift(giftData);

    log.info("Gift deposited successfully via direct flow", {
      operation: "handle_direct_gift_deposit_flow",
      giftId,
      userTgId,
      collectionId,
    });

    return {
      success: true,
      message: "Gift deposited successfully.",
      giftId,
    };
  } catch (error) {
    log.error("Failed to handle direct gift deposit flow", error, {
      operation: "handle_direct_gift_deposit_flow",
      userTgId,
      collectionId,
    });
    throw error;
  }
}

export async function depositGiftDirectlyForBot(params: {
  gift: GiftFromBot;
  userTgId: string;
  collectionId?: string;
}) {
  const { gift, userTgId, collectionId } = params;

  try {
    if (!userTgId) {
      throw new Error("User telegram ID is required");
    }

    if (!gift) {
      throw new Error("Gift data is required");
    }

    if (!gift.model || !gift.symbol || !gift.backdrop) {
      throw new Error("Gift must have model, symbol, and backdrop");
    }

    log.info("Starting direct gift deposit for bot", {
      operation: "deposit_gift_directly_for_bot",
      userTgId,
      collectionId,
      giftModel: gift.model,
    });

    return await handleDirectGiftDepositFlow(gift, userTgId, collectionId);
  } catch (error) {
    log.error("Failed to deposit gift directly for bot", error, {
      operation: "deposit_gift_directly_for_bot",
      userTgId,
      collectionId,
    });
    throw error;
  }
}
