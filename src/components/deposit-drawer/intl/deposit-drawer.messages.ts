import { defineMessages } from 'react-intl';

export const depositDrawerMessages = defineMessages({
  depositFunds: {
    id: 'depositDrawer.depositFunds',
    defaultMessage: 'Deposit Funds',
  },
  addTonToBalance: {
    id: 'depositDrawer.addTonToBalance',
    defaultMessage: 'Add TON to your marketplace balance',
  },
  loadingConfiguration: {
    id: 'depositDrawer.loadingConfiguration',
    defaultMessage: 'Loading configuration...',
  },
  depositInformation: {
    id: 'depositDrawer.depositInformation',
    defaultMessage: 'Deposit Information',
  },
  minimumDeposit: {
    id: 'depositDrawer.minimumDeposit',
    defaultMessage: 'Minimum deposit:',
  },
  depositFee: {
    id: 'depositDrawer.depositFee',
    defaultMessage: 'Deposit fee:',
  },
  depositProcessing: {
    id: 'depositDrawer.depositProcessing',
    defaultMessage: 'Deposit Processing',
  },
  youWillReceiveFundsWithin: {
    id: 'depositDrawer.youWillReceiveFundsWithin',
    defaultMessage: 'You will receive your funds within',
  },
  depositSuccess: {
    id: 'depositDrawer.depositSuccess',
    defaultMessage: 'Deposit Successful!',
  },
  depositCompleted: {
    id: 'depositDrawer.depositCompleted',
    defaultMessage: 'Your deposit has been completed successfully',
  },
  viewOnTonScan: {
    id: 'depositDrawer.viewOnTonScan',
    defaultMessage: 'View on TON Scan',
  },
  copyTransactionHash: {
    id: 'depositDrawer.copyTransactionHash',
    defaultMessage: 'Copy Transaction Hash',
  },
  transactionHashCopied: {
    id: 'depositDrawer.transactionHashCopied',
    defaultMessage: 'Transaction hash copied to clipboard',
  },
  close: {
    id: 'depositDrawer.close',
    defaultMessage: 'Close',
  },
  processingYourDeposit: {
    id: 'depositDrawer.processingYourDeposit',
    defaultMessage: 'Processing your deposit...',
  },
});
