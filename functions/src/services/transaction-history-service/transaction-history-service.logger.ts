import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const transactionHistoryServiceLogger = createServiceLogger(
  "transaction-history-service"
);

export const TransactionHistoryServiceLogger = {
  logTransactionCreated({
    userId,
    txType,
    amount,
  }: {
    userId: string;
    txType: string;
    amount: number;
  }) {
    transactionHistoryServiceLogger.logInfo(
      "Transaction history record created",
      LogOperations.CREATE_TRANSACTION_RECORD,
      {
        userId,
        txType,
        amount,
      }
    );
  },
};

export function logTransactionHistoryError({
  error,
  operation,
  userId,
  txType,
}: {
  error: unknown;
  operation: string;
  userId?: string;
  txType?: string;
}) {
  transactionHistoryServiceLogger.logError(
    `Error in transaction history service: ${operation}`,
    error,
    LogOperations.TRANSACTION_HISTORY,
    {
      operation,
      userId,
      txType,
    }
  );
}
