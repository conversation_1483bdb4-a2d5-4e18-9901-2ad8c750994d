'use client';

import { Loader2, Settings } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Form } from '@/components/ui/form';

import { FeesFormField } from '../fees-management/fees-form-field';
import { feeFieldGroups } from '../fees-management/fees-management-types';
import { getValidationWarnings } from '../fees-management/fees-management-utils';
import { FeesSummaryDisplay } from '../fees-management/fees-summary-display';
import { FeesValidationWarnings } from '../fees-management/fees-validation-warnings';
import { useFeesManagement } from '../fees-management/use-fees-management';

export const AppConfigManagement = () => {
  const { form, isLoading, isLoadingData, onSubmit } = useFeesManagement();
  const validationWarnings = getValidationWarnings(form.watch());

  if (isLoadingData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            App Configuration
          </CardTitle>
          <CardDescription>
            Configure marketplace fees and settings
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading configuration...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <FeesValidationWarnings warnings={validationWarnings} />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {feeFieldGroups.map((group) => (
            <Card key={group.title}>
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">{group.title}</CardTitle>
                <CardDescription>{group.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {group.fields.map((config) => (
                    <FeesFormField
                      key={config.name}
                      control={form.control}
                      config={config}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Configuration Summary</CardTitle>
              <CardDescription>
                Review your current configuration settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FeesSummaryDisplay watch={form.watch} />
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button type="submit" disabled={isLoading} size="lg">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating Configuration...
                </>
              ) : (
                'Save Configuration'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
