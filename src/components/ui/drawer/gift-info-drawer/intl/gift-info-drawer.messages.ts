import { defineMessages } from 'react-intl';

export const giftInfoDrawerMessages = defineMessages({
  sendGiftToRelayer: {
    id: 'giftInfoDrawer.sendGiftToRelayer',
    defaultMessage: 'Send Gift to <PERSON><PERSON>',
  },
  claimYourGift: {
    id: 'giftInfoDrawer.claimYourGift',
    defaultMessage: 'Claim Your Gift',
  },
  sendGiftSteps: {
    id: 'giftInfoDrawer.sendGiftSteps',
    defaultMessage: 'Follow these steps to send your gift to the relayer',
  },
  claimGiftSteps: {
    id: 'giftInfoDrawer.claimGiftSteps',
    defaultMessage: 'Follow these steps to claim your gift from the relayer',
  },
  pressMyBuyOrders: {
    id: 'giftInfoDrawer.pressMyBuyOrders',
    defaultMessage: 'Press on "My Buy Orders" button',
  },
  selectYourOrder: {
    id: 'giftInfoDrawer.selectYourOrder',
    defaultMessage: 'Select your order you want to get',
  },
});
