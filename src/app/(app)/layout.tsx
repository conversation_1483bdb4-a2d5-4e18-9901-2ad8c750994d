'use client';

import { TonConnectUIProvider } from '@tonconnect/ui-react';
import { useEffect, useState } from 'react';
import { useLocalStorage } from 'usehooks-ts';

import { RootLayoutHeader } from '@/app/(app)/root-layout-header';
import { TelegramProvider } from '@/components/TelegramProvider';
import { WelcomeModal } from '@/components/welcome-modal';
import { LocalStorageKeys } from '@/core.constants';
import { WALLET_MANIFEST_URL } from '@/utils/ton-constants';

import RootLayoutFooter from './root-layout-footer';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [welcomeModalShown] = useLocalStorage(
    LocalStorageKeys.WELCOME_MODAL_SHOWN,
    false,
  );
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);

  useEffect(() => {
    if (!welcomeModalShown) {
      setShowWelcomeModal(true);
    }
  }, [welcomeModalShown]);

  return (
    <TelegramProvider>
      <TonConnectUIProvider manifestUrl={WALLET_MANIFEST_URL}>
        <RootLayoutHeader />
        <div className="min-h-screen flex flex-col pt-[73px] pb-16">
          <main className="flex-1 p-2">
            <div className="max-w-6xl mx-auto">{children}</div>
          </main>
        </div>

        <RootLayoutFooter />

        <WelcomeModal
          open={showWelcomeModal}
          onOpenChange={setShowWelcomeModal}
        />
      </TonConnectUIProvider>
    </TelegramProvider>
  );
}
