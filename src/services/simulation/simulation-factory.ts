import { SimulationService } from "./simulation-service";
import { createSimulationConfig } from "./simulation-config";

let simulationServiceInstance: SimulationService | null = null;

export function getSimulationService(): SimulationService {
  if (!simulationServiceInstance) {
    const config = createSimulationConfig();
    simulationServiceInstance = new SimulationService(config);
  }
  return simulationServiceInstance;
}

export function resetSimulationService(): void {
  simulationServiceInstance = null;
}
