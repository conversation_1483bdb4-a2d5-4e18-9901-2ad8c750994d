'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { Plus } from 'lucide-react';
import { useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';

interface InsufficientBalanceMessageProps {
  message: string;
  onTopUp: () => void;
  className?: string;
}

export function InsufficientBalanceMessage({
  message,
  onTopUp,
  className = '',
}: InsufficientBalanceMessageProps) {
  const { formatMessage: t } = useIntl();

  return (
    <div className={`text-center space-y-3 ${className}`}>
      <Caption level="2" weight="3" className="text-[#ec3942]">
        {message}
      </Caption>
      <Button
        onClick={onTopUp}
        size="sm"
        className="gap-2 bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white border-0"
      >
        <Plus className="w-4 h-4" />
        {t({
          id: 'insufficientBalance.topUp',
          defaultMessage: 'Top up balance',
        })}
      </Button>
    </div>
  );
}
