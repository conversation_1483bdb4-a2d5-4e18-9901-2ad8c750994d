terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.28.0"
    }
  }
}

provider "google" {
  project = "marketplace-362c0"
  region  = "us-central1"
}

resource "google_storage_bucket" "terraform_state" {
  name          = "marketplace-362c0-terraform-state"
  location      = "US"
  force_destroy = false

  versioning {
    enabled = true
  }

  uniform_bucket_level_access = true

  lifecycle_rule {
    condition {
      age = 3650  # 10 years
    }
    action {
      type = "Delete"
    }
  }

  lifecycle {
    prevent_destroy = true
  }
}

provider "google" {
  alias   = "europe"
  project = "marketplace-362c0"
  region  = "europe-central2"
}

resource "google_storage_bucket" "terraform_state_dev" {
  provider      = google.europe
  name          = "marketplace-europe-362c0-terraform-state"
  location      = "EU"
  force_destroy = false

  versioning {
    enabled = true
  }

  uniform_bucket_level_access = true

  lifecycle_rule {
    condition {
      age = 3650  # 10 year for dev
    }
    action {
      type = "Delete"
    }
  }

  lifecycle {
    prevent_destroy = true
  }
}