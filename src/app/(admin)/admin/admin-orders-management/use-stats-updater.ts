import { useCallback } from 'react';

import type { AdminOrderStats } from '@/api/admin-api';
import { useToast } from '@/hooks/use-toast';

import { statsConfig } from './stats-config';
import type { StatType } from './types';

interface UseStatsUpdaterProps {
  stats: AdminOrderStats | null;
  setStats: React.Dispatch<React.SetStateAction<AdminOrderStats | null>>;
  setUpdatingStats: React.Dispatch<React.SetStateAction<Set<string>>>;
}

export function useStatsUpdater({
  stats,
  setStats,
  setUpdatingStats,
}: UseStatsUpdaterProps) {
  const { toast } = useToast();

  const updateStat = useCallback(
    async (statType: StatType) => {
      if (!stats) return;

      const config = statsConfig[statType];
      if (!config) {
        console.warn(`Unknown stat type: ${statType}`);
        return;
      }

      setUpdatingStats((prev) => new Set(prev).add(statType));

      try {
        const newValue = await config.apiCall();

        setStats((prev) => {
          if (!prev) return null;

          // Apply the update function and then set the actual value
          const updated = config.updateStats(prev);

          // Set the actual value in the appropriate location
          return setValueInStats(updated, statType, newValue);
        });

        toast({
          title: 'Success',
          description: `${statType} updated successfully`,
        });
      } catch (error) {
        console.error(`Error updating ${statType}:`, error);
        toast({
          title: 'Error',
          description: `Failed to update ${statType}`,
          variant: 'destructive',
        });
      } finally {
        setUpdatingStats((prev) => {
          const newSet = new Set(prev);
          newSet.delete(statType);
          return newSet;
        });
      }
    },
    [stats, setStats, setUpdatingStats, toast],
  );

  return { updateStat };
}

function setValueInStats(
  stats: AdminOrderStats,
  statType: StatType,
  value: number,
): AdminOrderStats {
  switch (statType) {
    case 'totalOrders':
      return { ...stats, totalOrders: value };
    case 'nonAdminActive':
      return {
        ...stats,
        nonAdminOrders: { ...stats.nonAdminOrders, active: value },
      };
    case 'nonAdminPaid':
      return {
        ...stats,
        nonAdminOrders: { ...stats.nonAdminOrders, paid: value },
      };
    case 'nonAdminGiftSent':
      return {
        ...stats,
        nonAdminOrders: { ...stats.nonAdminOrders, giftSentToRelayer: value },
      };
    case 'nonAdminCancelled':
      return {
        ...stats,
        nonAdminOrders: { ...stats.nonAdminOrders, cancelled: value },
      };
    case 'nonAdminFulfilled':
      return {
        ...stats,
        nonAdminOrders: { ...stats.nonAdminOrders, fulfilled: value },
      };
    case 'adminActive':
      return {
        ...stats,
        adminOrders: { ...stats.adminOrders, active: value },
      };
    case 'adminPaid':
      return {
        ...stats,
        adminOrders: { ...stats.adminOrders, paid: value },
      };
    case 'adminGiftSent':
      return {
        ...stats,
        adminOrders: { ...stats.adminOrders, giftSentToRelayer: value },
      };
    case 'adminCancelled':
      return {
        ...stats,
        adminOrders: { ...stats.adminOrders, cancelled: value },
      };
    case 'adminFulfilled':
      return {
        ...stats,
        adminOrders: { ...stats.adminOrders, fulfilled: value },
      };
    default:
      return stats;
  }
}
