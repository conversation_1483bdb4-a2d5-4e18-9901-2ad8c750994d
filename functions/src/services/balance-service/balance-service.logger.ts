import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const balanceServiceLogger = createServiceLogger("balance-service");

export const BalanceServiceLogger = {
  logBalanceUpdate({
    userId,
    sumChange,
    lockedChange,
    operation,
  }: {
    userId: string;
    sumChange: number;
    lockedChange: number;
    operation: string;
  }) {
    balanceServiceLogger.logInfo(
      `Balance updated for user ${userId}`,
      LogOperations.BALANCE_OPERATION,
      {
        userId,
        sumChange,
        lockedChange,
        operation,
      }
    );
  },

  logInsufficientFunds({
    userId,
    requiredAmount,
    availableBalance,
    operation,
  }: {
    userId: string;
    requiredAmount: number;
    availableBalance: number;
    operation: string;
  }) {
    balanceServiceLogger.logInfo(
      `Insufficient funds for user ${userId}`,
      LogOperations.INSUFFICIENT_FUNDS,
      {
        userId,
        requiredAmount,
        availableBalance,
        operation,
      }
    );
  },

  logFundsValidated({
    userId,
    amount,
    operation,
  }: {
    userId: string;
    amount: number;
    operation: string;
  }) {
    balanceServiceLogger.logInfo(
      `Funds validated for user ${userId}`,
      LogOperations.FUNDS_VALIDATION,
      {
        userId,
        amount,
        operation,
      }
    );
  },

  logBalanceServiceError({
    error,
    operation,
    userId,
  }: {
    error: unknown;
    operation: string;
    userId?: string;
  }) {
    balanceServiceLogger.logError(
      `Error in balance service: ${operation}`,
      error,
      LogOperations.BALANCE_OPERATION,
      {
        userId,
      }
    );
  },
};
