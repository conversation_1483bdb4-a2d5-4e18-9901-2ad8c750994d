import { defineMessages } from 'react-intl';

export const resellOrderPriceDrawerMessages = defineMessages({
  setResalePrice: {
    id: 'marketplace.resell.setResalePrice',
    defaultMessage: 'Set Resale Price',
  },
  setResalePriceSubtitle: {
    id: 'marketplace.resell.setResalePriceSubtitle',
    defaultMessage:
      'Set your price for reselling this order on the secondary market',
  },
  originalPrice: {
    id: 'marketplace.resell.originalPrice',
    defaultMessage: 'Original Price',
  },
  resalePriceTon: {
    id: 'marketplace.resell.resalePriceTon',
    defaultMessage: 'Resale Price (TON)',
  },
  minimumTonPlaceholder: {
    id: 'marketplace.resell.minimumTonPlaceholder',
    defaultMessage: 'Minimum {minPrice} TON',
  },
  minimumPrice: {
    id: 'marketplace.resell.minimumPrice',
    defaultMessage: 'Minimum: {minPrice} TON',
  },
  valid: {
    id: 'marketplace.resell.valid',
    defaultMessage: '✓ Valid',
  },
  tooHigh: {
    id: 'marketplace.resell.tooHigh',
    defaultMessage: '✗ Too high',
  },
  tooLow: {
    id: 'marketplace.resell.tooLow',
    defaultMessage: '✗ Too low',
  },
  importantNotice: {
    id: 'marketplace.resell.importantNotice',
    defaultMessage: 'Important Notice',
  },
  importantNoticeDescription: {
    id: 'marketplace.resell.importantNoticeDescription',
    defaultMessage:
      'Once you set a resale price, your order will be listed on the secondary market. Other users will be able to purchase it at your set price.',
  },
  setResalePriceButton: {
    id: 'marketplace.resell.setResalePriceButton',
    defaultMessage: 'Set Resale Price',
  },
  settingPrice: {
    id: 'marketplace.resell.settingPrice',
    defaultMessage: 'Setting Price...',
  },
  cancel: {
    id: 'marketplace.resell.cancel',
    defaultMessage: 'Cancel',
  },
});
