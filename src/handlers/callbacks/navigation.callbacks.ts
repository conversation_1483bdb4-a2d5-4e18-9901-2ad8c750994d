import { Context } from "telegraf";
import { T } from "../../i18n";
import { botMessages } from "../../intl/messages";
import {
  createMainKeyboard,
  createMarketplaceInlineKeyboard,
} from "../../utils/keyboards";

export const handleOpenMarketplaceCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    T(ctx, botMessages.openingMarketplace.id),
    createMarketplaceInlineKeyboard(ctx)
  );
};

export const handleBackToMenuCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(T(ctx, botMessages.backToMenu.id), createMainKeyboard(ctx));
};
