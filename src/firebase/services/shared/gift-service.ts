import { getFirestore } from "../../firebase-admin";
import {
  GiftEntity,
  GiftStatus,
  GIFTS_COLLECTION_NAME,
  AppDate,
} from "../../../mikerudenko/marketplace-shared";
import { log } from "../../../utils/logger";

export async function createGift(
  giftData: Omit<GiftEntity, "id" | "createdAt" | "updatedAt">,
  batch?: FirebaseFirestore.WriteBatch
) {
  try {
    const db = getFirestore();
    const giftRef = db.collection(GIFTS_COLLECTION_NAME).doc();

    const giftEntity: GiftEntity = {
      id: giftRef.id,
      ...giftData,
      createdAt: new Date() as AppDate,
      updatedAt: new Date() as AppDate,
    };

    if (batch) {
      batch.set(giftRef, giftEntity);
    } else {
      await giftRef.set(giftEntity);
    }

    log.info("Gift created successfully", {
      operation: "create_gift",
      giftId: giftRef.id,
      ownerTgId: giftData.owner_tg_id,
      collectionId: giftData.collectionId,
    });

    return giftRef.id;
  } catch (error) {
    log.error("Failed to create gift", error, {
      operation: "create_gift",
      ownerTgId: giftData.owner_tg_id,
      collectionId: giftData.collectionId,
    });
    throw new Error(`Failed to create gift: ${error}`);
  }
}

export async function updateGiftOwnership(
  giftId: string,
  newOwnerTgId: string,
  status: GiftStatus,
  batch?: FirebaseFirestore.WriteBatch
) {
  try {
    const db = getFirestore();
    const giftRef = db.collection(GIFTS_COLLECTION_NAME).doc(giftId);

    const updateData = {
      owner_tg_id: newOwnerTgId,
      status,
      updatedAt: new Date() as AppDate,
    };

    if (batch) {
      batch.update(giftRef, updateData);
    } else {
      await giftRef.update(updateData);
    }

    log.info("Gift ownership updated successfully", {
      operation: "update_gift_ownership",
      giftId,
      newOwnerTgId,
      status,
    });
  } catch (error) {
    log.error("Failed to update gift ownership", error, {
      operation: "update_gift_ownership",
      giftId,
      newOwnerTgId,
      status,
    });
    throw new Error(`Failed to update gift ownership: ${error}`);
  }
}

export async function getGiftById(giftId: string): Promise<GiftEntity | null> {
  try {
    const db = getFirestore();
    const giftDoc = await db
      .collection(GIFTS_COLLECTION_NAME)
      .doc(giftId)
      .get();

    if (!giftDoc.exists) {
      return null;
    }

    return { id: giftDoc.id, ...giftDoc.data() } as GiftEntity;
  } catch (error) {
    log.error("Failed to get gift by ID", error, {
      operation: "get_gift_by_id",
      giftId,
    });
    throw new Error(`Failed to get gift by ID: ${error}`);
  }
}

export async function markGiftAsWithdrawn(
  giftId: string,
  batch?: FirebaseFirestore.WriteBatch
): Promise<void> {
  try {
    const db = getFirestore();
    const giftRef = db.collection(GIFTS_COLLECTION_NAME).doc(giftId);

    const updateData = {
      status: GiftStatus.WITHDRAWN,
      updatedAt: new Date() as AppDate,
    };

    if (batch) {
      batch.update(giftRef, updateData);
    } else {
      await giftRef.update(updateData);
    }

    log.info("Gift marked as withdrawn successfully", {
      operation: "mark_gift_as_withdrawn",
      giftId,
    });
  } catch (error) {
    log.error("Failed to mark gift as withdrawn", error, {
      operation: "mark_gift_as_withdrawn",
      giftId,
    });
    throw new Error(`Failed to mark gift as withdrawn: ${error}`);
  }
}
