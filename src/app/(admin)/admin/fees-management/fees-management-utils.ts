import type { FeesFormData } from './fees-management-types';

export const formatBpsToPercent = (bps: number | string): string => {
  if (!bps) return '0.00';
  return (Number(bps) / 100).toFixed(2);
};

export const formatTonValue = (ton: number | string): string => {
  if (!ton) return '0.000';

  return Number(ton).toFixed(4);
};

export const getValidationWarnings = (values: FeesFormData): string[] => {
  const warnings: string[] = [];

  if (values.referrer_fee > values.purchase_fee) {
    warnings.push(
      `Referral fee (${formatBpsToPercent(values.referrer_fee)}%) exceeds purchase fee (${formatBpsToPercent(values.purchase_fee)}%). Note: Referral fees only apply to purchase fees, not resell fees.`,
    );
  }

  if (values.resell_purchase_fee_for_seller > 5000) {
    warnings.push(
      `Resell fee for seller (${formatBpsToPercent(values.resell_purchase_fee_for_seller)}%) exceeds reasonable limits (50%)`,
    );
  }

  return warnings;
};
