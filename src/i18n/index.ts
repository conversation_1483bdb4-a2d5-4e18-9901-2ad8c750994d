import { createIntl, createIntlCache } from "@formatjs/intl";

const cache = createIntlCache();

export enum SupportedLocale {
  EN = "en",
  UA = "ua",
  RU = "ru",
}

const DEFAULT_LOCALE = SupportedLocale.EN;

export const AVAILABLE_LOCALES = Object.values(SupportedLocale);

let messages: Record<SupportedLocale, Record<string, string>> = {
  [SupportedLocale.EN]: {},
  [SupportedLocale.UA]: {},
  [SupportedLocale.RU]: {},
};

try {
  messages[SupportedLocale.EN] = require("../translations/en.json");
} catch {
  console.warn("Could not load English messages, using empty object");
}

try {
  messages[SupportedLocale.UA] = require("../translations/ua.json");
} catch {
  console.warn("Could not load Ukrainian messages, using empty object");
}

try {
  messages[SupportedLocale.RU] = require("../translations/ru.json");
} catch {
  console.warn("Could not load Russian messages, using empty object");
}

export const createIntlInstance = (
  locale: SupportedLocale = DEFAULT_LOCALE
) => {
  return createIntl(
    {
      locale,
      messages: messages[locale],
    },
    cache
  );
};

const defaultIntl = createIntlInstance();

export const T = (
  ctx: any,
  id: string,
  values: Record<string, any> = {}
): string => {
  const userLanguage = ctx?.userLanguage || DEFAULT_LOCALE;
  const intl =
    userLanguage === DEFAULT_LOCALE
      ? defaultIntl
      : createIntlInstance(userLanguage);
  const result = intl.formatMessage({ id }, values);
  return Array.isArray(result) ? result.join("") : result;
};

export const TNoContext = (
  id: string,
  values: Record<string, any> = {},
  locale: SupportedLocale = DEFAULT_LOCALE
): string => {
  const intl =
    locale === DEFAULT_LOCALE ? defaultIntl : createIntlInstance(locale);
  const result = intl.formatMessage({ id }, values);
  return Array.isArray(result) ? result.join("") : result;
};

export const formatMessage = T;

export { defaultIntl as intl };
