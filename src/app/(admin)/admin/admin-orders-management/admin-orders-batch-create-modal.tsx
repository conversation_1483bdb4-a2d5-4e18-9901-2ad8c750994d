import { useState } from 'react';
import { useLocalStorage } from 'usehooks-ts';

import { Button } from '@/components/ui/button';
import { CollectionSelect } from '@/components/ui/collection-select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LocalStorageKeys } from '@/core.constants';
import { useToast } from '@/hooks/use-toast';
import { useRootContext } from '@/root-context';
import { createBatchOrdersWithProgress } from '@/services/admin-orders-service';

type OrderType = 'buy' | 'sell';

interface BatchCreateFormData {
  orderType: OrderType;
  totalOrders: string;
  minPrice: string;
  maxPrice: string;
  priceStep: string;
  useRandomCollection: boolean;
  specificCollectionId: string;
}

interface AdminOrdersBatchCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => Promise<void>;
}

export function AdminOrdersBatchCreateModal({
  isOpen,
  onClose,
  onSuccess,
}: AdminOrdersBatchCreateModalProps) {
  const { toast } = useToast();
  const { collections } = useRootContext();
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [createdCount, setCreatedCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [formData, setFormData] = useState<BatchCreateFormData>({
    orderType: 'buy',
    totalOrders: '10',
    minPrice: '1',
    maxPrice: '10',
    priceStep: '0',
    useRandomCollection: true,
    specificCollectionId: '',
  });

  const handleInputChange = (
    field: keyof BatchCreateFormData,
    value: string,
  ) => {
    // Allow only numbers and decimal points
    const sanitizedValue = value.replace(/[^0-9.]/g, '');
    setFormData((prev) => ({
      ...prev,
      [field]: sanitizedValue,
    }));
  };

  const validateForm = (): string | null => {
    const totalOrders = parseFloat(formData.totalOrders);
    const minPrice = parseFloat(formData.minPrice);
    const maxPrice = parseFloat(formData.maxPrice);
    const priceStep = parseFloat(formData.priceStep);

    if (isNaN(totalOrders) || totalOrders <= 0) {
      return 'Total orders must be a valid number greater than 0';
    }
    if (isNaN(minPrice) || isNaN(maxPrice) || minPrice <= 0 || maxPrice <= 0) {
      return 'Prices must be valid numbers greater than 0';
    }
    if (minPrice > maxPrice) {
      return 'Max price must be greater than or equal to min price';
    }
    if (isNaN(priceStep) || priceStep < 0) {
      return 'Price step must be a valid number greater than or equal to 0';
    }
    if (!formData.useRandomCollection && !formData.specificCollectionId) {
      return 'Please select a specific collection or use random collection';
    }
    return null;
  };

  const handleSubmit = async () => {
    const validationError = validateForm();
    if (validationError) {
      toast({
        title: 'Validation Error',
        description: validationError,
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    setProgress(0);
    setCreatedCount(0);

    const totalOrders = parseFloat(formData.totalOrders);
    setTotalCount(totalOrders);

    try {
      const buyOrdersPercentage = formData.orderType === 'buy' ? 100 : 0;
      const sellOrdersPercentage = formData.orderType === 'sell' ? 100 : 0;

      await createBatchOrdersWithProgress(
        {
          totalOrders,
          buyOrdersPercentage,
          sellOrdersPercentage,
          minPrice: parseFloat(formData.minPrice),
          maxPrice: parseFloat(formData.maxPrice),
          priceStep: parseFloat(formData.priceStep),
          useRandomCollection: formData.useRandomCollection,
          specificCollectionId: formData.specificCollectionId || undefined,
        },
        (created, total) => {
          setCreatedCount(created);
          setProgress((created / total) * 100);
        },
      );

      const orderTypeText = formData.orderType === 'buy' ? 'buy' : 'sell';
      toast({
        title: 'Success',
        description: `Successfully created ${totalOrders} ${orderTypeText} orders`,
      });

      await onSuccess();
    } catch (error) {
      console.error('Error creating batch orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to create batch orders',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
      setProgress(0);
      setCreatedCount(0);
      setTotalCount(0);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create Admin Orders</DialogTitle>
          <DialogDescription>
            Create multiple random orders with admin accounts
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <Tabs
            value={formData.orderType}
            onValueChange={(value) =>
              setFormData((prev) => ({
                ...prev,
                orderType: value as OrderType,
              }))
            }
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="buy">Create Buy Orders</TabsTrigger>
              <TabsTrigger value="sell">Create Sell Orders</TabsTrigger>
            </TabsList>

            <TabsContent value="buy" className="mt-4">
              <div className="text-sm text-muted-foreground mb-4">
                Creating buy orders only
              </div>
            </TabsContent>

            <TabsContent value="sell" className="mt-4">
              <div className="text-sm text-muted-foreground mb-4">
                Creating sell orders only
              </div>
            </TabsContent>
          </Tabs>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="totalOrders" className="text-right">
              Total Orders
            </Label>
            <Input
              id="totalOrders"
              type="text"
              value={formData.totalOrders}
              onChange={(e) => handleInputChange('totalOrders', e.target.value)}
              className="col-span-3"
              placeholder="e.g., 10"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="minPrice" className="text-right">
              Min Price (TON)
            </Label>
            <Input
              id="minPrice"
              type="text"
              value={formData.minPrice}
              onChange={(e) => handleInputChange('minPrice', e.target.value)}
              className="col-span-3"
              placeholder="e.g., 1.0"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="maxPrice" className="text-right">
              Max Price (TON)
            </Label>
            <Input
              id="maxPrice"
              type="text"
              value={formData.maxPrice}
              onChange={(e) => handleInputChange('maxPrice', e.target.value)}
              className="col-span-3"
              placeholder="e.g., 10.0"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="priceStep" className="text-right">
              Price Step
            </Label>
            <Input
              id="priceStep"
              type="text"
              value={formData.priceStep}
              onChange={(e) => handleInputChange('priceStep', e.target.value)}
              className="col-span-3"
              placeholder="e.g., 0 (for single price)"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="useRandomCollection" className="text-right">
              Random Collection
            </Label>
            <div className="col-span-3 flex items-center space-x-2">
              <Switch
                id="useRandomCollection"
                checked={formData.useRandomCollection}
                onCheckedChange={(checked) =>
                  setFormData((prev) => ({
                    ...prev,
                    useRandomCollection: checked,
                    specificCollectionId: checked
                      ? ''
                      : prev.specificCollectionId,
                  }))
                }
              />
              <Label htmlFor="useRandomCollection" className="text-sm">
                Use random collection selection
              </Label>
            </div>
          </div>

          {!formData.useRandomCollection && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="collection" className="text-right">
                Collection
              </Label>
              <div className="col-span-3">
                <CollectionSelect
                  hideLabel
                  animated={isAnimatedCollection}
                  collections={collections}
                  value={formData.specificCollectionId}
                  onValueChange={(value) =>
                    setFormData((prev) => ({
                      ...prev,
                      specificCollectionId: value,
                    }))
                  }
                  placeholder="Select collection"
                  className="w-full"
                />
              </div>
            </div>
          )}
        </div>

        {loading && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Creating orders...</span>
              <span>
                {createdCount} / {totalCount}
              </span>
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading
              ? `Creating... (${createdCount}/${totalCount})`
              : 'Create Orders'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
