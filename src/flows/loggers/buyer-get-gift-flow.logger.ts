import { log } from "../../utils/logger";
import { LogOperations } from "../../constants/bot-commands";

export const BuyerGetGiftFlowLogger = {
  logBuyerGiftRequestStarted(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.info("Processing buyer gift request", {
      operation: LogOperations.BUYER_GET_GIFT,
      ...params,
    });
  },

  logOrderNotFoundForBuyerRequest(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.warn("No existing order found for buyer request", {
      operation: LogOperations.BUYER_GET_GIFT,
      ...params,
    });
  },

  logUserNotAuthorizedBuyer(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    originalBuyerId?: string;
  }) {
    log.warn("User not authorized - not original buyer", {
      operation: LogOperations.BUYER_GET_GIFT,
      ...params,
    });
  },

  logOrderNotReadyForGiftDelivery(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    orderStatus: string;
  }) {
    log.info("Order is not ready for gift delivery", {
      operation: LogOperations.BUYER_GET_GIFT,
      ...params,
    });
  },

  logNoGiftAvailableForBuyer(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.warn("No gift available to transfer to buyer", {
      operation: LogOperations.BUYER_GET_GIFT,
      ...params,
    });
  },

  logMissingBusinessConnectionForBuyer(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.error("Missing business connection ID for gift transfer", {
      operation: LogOperations.BUYER_GET_GIFT,
      ...params,
    });
  },

  logGiftTransferToBuyerStarted(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
    businessConnectionId: string;
    giftToTransferToBuyer: string;
  }) {
    log.info("Starting gift transfer to buyer", {
      operation: LogOperations.BUYER_GET_GIFT,
      ...params,
    });
  },

  logGiftTransferToBuyerCompleted(params: {
    chat_id: string | number;
    userId: string;
    pendingOrderId: string;
  }) {
    log.info("Gift transfer to buyer completed successfully", {
      operation: LogOperations.BUYER_GET_GIFT,
      ...params,
    });
  },
};
