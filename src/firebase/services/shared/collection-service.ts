import { getFirestore } from "../../firebase-admin";
import {
  COLLECTION_NAME,
  CollectionEntity,
  CollectionStatus,
  OrderEntity,
} from "../../../mikerudenko/marketplace-shared";
import { log } from "../../../utils/logger";

export async function getMarketCollectionIds(collectionIds: string[]) {
  if (collectionIds.length === 0) {
    return new Set();
  }

  try {
    const db = getFirestore();
    const marketCollections = new Set<string>();

    const collectionsQuery = await db
      .collection(COLLECTION_NAME)
      .where("__name__", "in", collectionIds)
      .get();

    collectionsQuery.forEach((doc) => {
      const collection = doc.data() as CollectionEntity;
      if (collection.status === CollectionStatus.MARKET) {
        marketCollections.add(doc.id);
      }
    });

    return marketCollections;
  } catch (error) {
    log.error("Failed to get market collection IDs", error, {
      operation: "get_market_collection_ids",
      collectionIds,
    });
    throw new Error(`Failed to get market collection IDs: ${error}`);
  }
}

export async function filterOrdersByMarketCollections(
  orders: OrderEntity[]
): Promise<OrderEntity[]> {
  if (orders.length === 0) {
    return [];
  }

  try {
    const collectionIds = [
      ...new Set(orders.map((order) => order.collectionId)),
    ];
    const marketCollections = await getMarketCollectionIds(collectionIds);
    return orders.filter((order) => marketCollections.has(order.collectionId));
  } catch (error) {
    log.error("Failed to filter orders by market collections", error, {
      operation: "filter_orders_by_market_collections",
      orderCount: orders.length,
    });
    throw new Error(`Failed to filter orders by market collections: ${error}`);
  }
}

export function extractCollectionIds(orders: OrderEntity[]): string[] {
  return [...new Set(orders.map((order) => order.collectionId))];
}
