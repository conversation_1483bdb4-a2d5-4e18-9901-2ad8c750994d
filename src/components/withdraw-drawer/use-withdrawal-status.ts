import { useCallback, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import {
  firebaseTimestampToDate,
  type UserEntity,
} from '@/mikerudenko/marketplace-shared';

export interface WithdrawalStatus {
  currentWithdrawn: number;
  remainingLimit: number;
  maxLimit: number;
  resetAt: string;
}

export interface UseWithdrawalStatusResult {
  withdrawalStatus: WithdrawalStatus | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useWithdrawalStatus(
  currentUser: UserEntity | null,
  maxWithdrawalAmount: number = Number.MAX_SAFE_INTEGER,
): UseWithdrawalStatusResult {
  const { formatMessage: t } = useIntl();
  const [withdrawalStatus, setWithdrawalStatus] =
    useState<WithdrawalStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const calculateWithdrawalStatus = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!currentUser) {
        setWithdrawalStatus(null);
        return;
      }

      const HOURS_24_IN_MS = 24 * 60 * 60 * 1000;
      const now = new Date();

      let currentWithdrawn = 0;
      let resetAt = new Date(now.getTime() + HOURS_24_IN_MS);

      // Check if user has withdrawal tracking data
      if (currentUser.withdrawal_24h) {
        const lastResetTime = firebaseTimestampToDate(
          currentUser.withdrawal_24h.lastResetAt,
        );
        const timeSinceReset = now.getTime() - lastResetTime.getTime();

        // If less than 24 hours have passed since last reset, use existing data
        if (timeSinceReset < HOURS_24_IN_MS) {
          currentWithdrawn = currentUser.withdrawal_24h.amount;
          resetAt = new Date(lastResetTime.getTime() + HOURS_24_IN_MS);
        }
        // If 24+ hours have passed, the limit has reset (currentWithdrawn stays 0)
      }

      const remainingLimit = Math.max(
        0,
        maxWithdrawalAmount - currentWithdrawn,
      );

      const status: WithdrawalStatus = {
        currentWithdrawn,
        remainingLimit,
        maxLimit: maxWithdrawalAmount,
        resetAt: resetAt.toISOString(),
      };

      setWithdrawalStatus(status);
    } catch (err) {
      console.error('Error calculating withdrawal status:', err);
      setError(
        err instanceof Error
          ? err.message
          : t({ id: 'errors.withdrawal.calculationFailed' }),
      );
    } finally {
      setLoading(false);
    }
  }, [currentUser, maxWithdrawalAmount, t]);

  useEffect(() => {
    calculateWithdrawalStatus();
  }, [calculateWithdrawalStatus]);

  return {
    withdrawalStatus,
    loading,
    error,
    refetch: calculateWithdrawalStatus,
  };
}
