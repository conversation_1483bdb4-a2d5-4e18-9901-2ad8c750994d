import { createServiceLogger } from "../logger/base-logger";
import { LogOperations } from "../constants";

const limitedCollectionsMonitorLogger = createServiceLogger(
  "limited-collections-monitor-function"
);

export const LimitedCollectionsMonitorLogger = {
  logMonitorStarted({ status }: { status: string }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Limited collections monitor started",
      LogOperations.MONITOR,
      { status }
    );
  },

  logCollectionsFound({ count }: { count: number }) {
    limitedCollectionsMonitorLogger.logInfo(
      `Found ${count} collections to process`,
      LogOperations.MONITOR,
      { count }
    );
  },

  logCollectionProcessed({
    collectionId,
    status,
  }: {
    collectionId: string;
    status: string;
  }) {
    limitedCollectionsMonitorLogger.logInfo(
      `Processed collection ${collectionId}`,
      LogOperations.MONITOR,
      { collectionId, status }
    );
  },

  logMonitorError({ error }: { error: unknown }) {
    limitedCollectionsMonitorLogger.logError(
      "Error in limited collections monitor",
      error,
      LogOperations.MONITOR,
      {}
    );
  },

  logMonitorTriggered({
    status,
    timestamp,
  }: {
    status: string;
    timestamp: string;
  }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Limited collections monitor triggered",
      LogOperations.MONITOR,
      { status, timestamp }
    );
  },

  logMonitorCompleted() {
    limitedCollectionsMonitorLogger.logInfo(
      "Limited collections monitor completed",
      LogOperations.MONITOR,
      {}
    );
  },

  logMonitorFailed({ error, status }: { error: unknown; status: string }) {
    limitedCollectionsMonitorLogger.logError(
      "Limited collections monitor failed",
      error,
      LogOperations.MONITOR,
      { status }
    );
  },

  logNoGiftsFound() {
    limitedCollectionsMonitorLogger.logInfo(
      "No gifts found in result",
      LogOperations.TELEGRAM_API,
      {}
    );
  },

  logLimitedGiftsFound({ count }: { count: number }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Found limited gifts from Telegram API",
      LogOperations.TELEGRAM_API,
      { count }
    );
  },

  logTelegramApiError({ error }: { error: unknown }) {
    limitedCollectionsMonitorLogger.logError(
      "Error fetching limited collections from Telegram",
      error,
      LogOperations.TELEGRAM_API,
      {}
    );
  },

  logCollectionNotFound({ collectionId }: { collectionId: string }) {
    limitedCollectionsMonitorLogger.logError(
      "Collection not found when trying to update to MARKET",
      new Error("Collection not found"),
      LogOperations.COLLECTION_UPDATE,
      { collectionId }
    );
  },

  logLaunchedAtSet({ collectionId }: { collectionId: string }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Setting launchedAt for collection",
      LogOperations.COLLECTION_UPDATE,
      { collectionId }
    );
  },

  logLaunchedAtExists({ collectionId }: { collectionId: string }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Collection already has launchedAt, skipping",
      LogOperations.COLLECTION_UPDATE,
      { collectionId }
    );
  },

  logCollectionUpdatedToMarket({ collectionId }: { collectionId: string }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Updated collection to MARKET status",
      LogOperations.COLLECTION_UPDATE,
      { collectionId }
    );
  },

  logNewCollectionCreated({ collectionId }: { collectionId: string }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Created new collection in Firestore",
      LogOperations.COLLECTION_CREATION,
      { collectionId }
    );
  },

  logCollectionCreationNotFound({ collectionId }: { collectionId: string }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Collection not found in Firestore, creating new collection",
      LogOperations.COLLECTION_CREATION,
      { collectionId }
    );
  },

  logCollectionCreationFailed({ collectionId }: { collectionId: string }) {
    limitedCollectionsMonitorLogger.logError(
      "Failed to create collection",
      new Error("Collection creation failed"),
      LogOperations.COLLECTION_CREATION,
      { collectionId }
    );
  },

  logCollectionStatusNotPremarket({
    collectionId,
    status,
  }: {
    collectionId: string;
    status: string;
  }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Collection status is not PREMARKET, skipping",
      LogOperations.COLLECTION_PROCESSING,
      { collectionId, status }
    );
  },

  logProcessingCollectionWithUpgradeStars({
    collectionId,
    upgradeStars,
  }: {
    collectionId: string;
    upgradeStars: string;
  }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Processing collection with upgradeStars",
      LogOperations.COLLECTION_PROCESSING,
      { collectionId, upgradeStars }
    );
  },

  logCollectionAlreadyExists({ collectionId }: { collectionId: string }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Collection already exists in Firestore",
      LogOperations.COLLECTION_EXISTENCE,
      { collectionId }
    );
  },

  logEnsureCollectionError({
    error,
    collectionId,
  }: {
    error: unknown;
    collectionId: string;
  }) {
    limitedCollectionsMonitorLogger.logError(
      "Error ensuring collection exists",
      error,
      LogOperations.COLLECTION_EXISTENCE,
      { collectionId }
    );
  },

  logNoLimitedCollections() {
    limitedCollectionsMonitorLogger.logInfo(
      "No limited collections found from Telegram API",
      LogOperations.MONITOR,
      {}
    );
  },

  logEnsureCollectionFailed({
    error,
    collectionId,
  }: {
    error: unknown;
    collectionId: string;
  }) {
    limitedCollectionsMonitorLogger.logError(
      "Failed to ensure collection exists",
      error,
      LogOperations.COLLECTION_EXISTENCE,
      { collectionId }
    );
  },

  logNoUpgradeableCollections() {
    limitedCollectionsMonitorLogger.logInfo(
      "No upgradeable limited collections found",
      LogOperations.MONITOR,
      {}
    );
  },

  logUpgradeableCollectionsFound({ count }: { count: number }) {
    limitedCollectionsMonitorLogger.logInfo(
      "Found upgradeable limited collections",
      LogOperations.MONITOR,
      { count }
    );
  },

  logProcessCollectionFailed({
    error,
    collectionId,
  }: {
    error: unknown;
    collectionId: string;
  }) {
    limitedCollectionsMonitorLogger.logError(
      "Failed to process collection",
      error,
      LogOperations.COLLECTION_PROCESSING,
      { collectionId }
    );
  },
};
