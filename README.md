## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev

```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON><PERSON><PERSON>](https://vercel.com/font), a new font family for Vercel. -

## Load CDN images

```
wget -r -np -nH --cut-dirs=1 -R "index.html*" https://cdn.changes.tg/gifts/
```

## Launch DEV GUIDE

1. in marketplace-ui run `bun dev` (is will run on port 3000)
2. in separate terminal run `redis-server`
3. in separate terminal run `ngrok http 3000` -> and get URL
4. go to `<you-path>/marketplace-bot/.env.local` and in that file update WEB_APP_URL to your ngrok URL, and BOT_TOKEN to your bot token
5. go to `<you-path>/marketplace-bot` and run `bun dev`
